<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Finite Impulse Response (FIR) Sparse Filters</title>
<title>CMSIS-DSP: Finite Impulse Response (FIR) Sparse Filters</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___f_i_r___sparse.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Finite Impulse Response (FIR) Sparse Filters</div>  </div>
<div class="ingroups"><a class="el" href="group__group_filters.html">Filtering Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga23a9284de5ee39406713b91d18ac8838"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___sparse.html#ga23a9284de5ee39406713b91d18ac8838">arm_fir_sparse_f32</a> (<a class="el" href="structarm__fir__sparse__instance__f32.html">arm_fir_sparse_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrc, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pDst, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pScratchIn, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga23a9284de5ee39406713b91d18ac8838"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the floating-point sparse FIR filter.  <a href="#ga23a9284de5ee39406713b91d18ac8838"></a><br/></td></tr>
<tr class="separator:ga23a9284de5ee39406713b91d18ac8838"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga86378a08a9d9e1e0e5de77843b34d396"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___sparse.html#ga86378a08a9d9e1e0e5de77843b34d396">arm_fir_sparse_init_f32</a> (<a class="el" href="structarm__fir__sparse__instance__f32.html">arm_fir_sparse_instance_f32</a> *S, uint16_t numTaps, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pState, int32_t *pTapDelay, uint16_t maxDelay, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga86378a08a9d9e1e0e5de77843b34d396"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point sparse FIR filter.  <a href="#ga86378a08a9d9e1e0e5de77843b34d396"></a><br/></td></tr>
<tr class="separator:ga86378a08a9d9e1e0e5de77843b34d396"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5eaa80bf72bcccef5a2c5fc6648d1baa"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___sparse.html#ga5eaa80bf72bcccef5a2c5fc6648d1baa">arm_fir_sparse_init_q15</a> (<a class="el" href="structarm__fir__sparse__instance__q15.html">arm_fir_sparse_instance_q15</a> *S, uint16_t numTaps, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pState, int32_t *pTapDelay, uint16_t maxDelay, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga5eaa80bf72bcccef5a2c5fc6648d1baa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q15 sparse FIR filter.  <a href="#ga5eaa80bf72bcccef5a2c5fc6648d1baa"></a><br/></td></tr>
<tr class="separator:ga5eaa80bf72bcccef5a2c5fc6648d1baa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9a0bb2134bc85d3e55c6be6d946ee634"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___sparse.html#ga9a0bb2134bc85d3e55c6be6d946ee634">arm_fir_sparse_init_q31</a> (<a class="el" href="structarm__fir__sparse__instance__q31.html">arm_fir_sparse_instance_q31</a> *S, uint16_t numTaps, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pState, int32_t *pTapDelay, uint16_t maxDelay, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga9a0bb2134bc85d3e55c6be6d946ee634"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q31 sparse FIR filter.  <a href="#ga9a0bb2134bc85d3e55c6be6d946ee634"></a><br/></td></tr>
<tr class="separator:ga9a0bb2134bc85d3e55c6be6d946ee634"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga98f5c1a097d4572ce4ff3b0c58ebcdbd"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___sparse.html#ga98f5c1a097d4572ce4ff3b0c58ebcdbd">arm_fir_sparse_init_q7</a> (<a class="el" href="structarm__fir__sparse__instance__q7.html">arm_fir_sparse_instance_q7</a> *S, uint16_t numTaps, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pState, int32_t *pTapDelay, uint16_t maxDelay, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga98f5c1a097d4572ce4ff3b0c58ebcdbd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q7 sparse FIR filter.  <a href="#ga98f5c1a097d4572ce4ff3b0c58ebcdbd"></a><br/></td></tr>
<tr class="separator:ga98f5c1a097d4572ce4ff3b0c58ebcdbd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2bffda2e156e72427e19276cd9c3d3cc"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___sparse.html#ga2bffda2e156e72427e19276cd9c3d3cc">arm_fir_sparse_q15</a> (<a class="el" href="structarm__fir__sparse__instance__q15.html">arm_fir_sparse_instance_q15</a> *S, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrc, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pScratchIn, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pScratchOut, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga2bffda2e156e72427e19276cd9c3d3cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q15 sparse FIR filter.  <a href="#ga2bffda2e156e72427e19276cd9c3d3cc"></a><br/></td></tr>
<tr class="separator:ga2bffda2e156e72427e19276cd9c3d3cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga03e9c2f0f35ad67d20bac66be9f920ec"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec">arm_fir_sparse_q31</a> (<a class="el" href="structarm__fir__sparse__instance__q31.html">arm_fir_sparse_instance_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pDst, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pScratchIn, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga03e9c2f0f35ad67d20bac66be9f920ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q31 sparse FIR filter.  <a href="#ga03e9c2f0f35ad67d20bac66be9f920ec"></a><br/></td></tr>
<tr class="separator:ga03e9c2f0f35ad67d20bac66be9f920ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae86c145efc2d9ec32dc6d8c1ad2ccb3c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___sparse.html#gae86c145efc2d9ec32dc6d8c1ad2ccb3c">arm_fir_sparse_q7</a> (<a class="el" href="structarm__fir__sparse__instance__q7.html">arm_fir_sparse_instance_q7</a> *S, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pSrc, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pDst, <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pScratchIn, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pScratchOut, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gae86c145efc2d9ec32dc6d8c1ad2ccb3c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q7 sparse FIR filter.  <a href="#gae86c145efc2d9ec32dc6d8c1ad2ccb3c"></a><br/></td></tr>
<tr class="separator:gae86c145efc2d9ec32dc6d8c1ad2ccb3c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>This group of functions implements sparse FIR filters. Sparse FIR filters are equivalent to standard FIR filters except that most of the coefficients are equal to zero. Sparse filters are used for simulating reflections in communications and audio applications.</p>
<p>There are separate functions for Q7, Q15, Q31, and floating-point data types. The functions operate on blocks of input and output data and each call to the function processes <code>blockSize</code> samples through the filter. <code>pSrc</code> and <code>pDst</code> points to input and output arrays respectively containing <code>blockSize</code> values.</p>
<dl class="section user"><dt>Algorithm: </dt><dd>The sparse filter instant structure contains an array of tap indices <code>pTapDelay</code> which specifies the locations of the non-zero coefficients. This is in addition to the coefficient array <code>b</code>. The implementation essentially skips the multiplications by zero and leads to an efficient realization. <pre>   
      y[n] = b[0] * x[n-pTapDelay[0]] + b[1] * x[n-pTapDelay[1]] + b[2] * x[n-pTapDelay[2]] + ...+ b[numTaps-1] * x[n-pTapDelay[numTaps-1]]    
  </pre> </dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="FIRSparse.gif" alt="FIRSparse.gif"/>
<div class="caption">
Sparse FIR filter. b[n] represents the filter coefficients</div></div>
 </dd></dl>
<dl class="section user"><dt></dt><dd><code>pCoeffs</code> points to a coefficient array of size <code>numTaps</code>; <code>pTapDelay</code> points to an array of nonzero indices and is also of size <code>numTaps</code>; <code>pState</code> points to a state array of size <code>maxDelay + blockSize</code>, where <code>maxDelay</code> is the largest offset value that is ever used in the <code>pTapDelay</code> array. Some of the processing functions also require temporary working buffers.</dd></dl>
<dl class="section user"><dt>Instance Structure </dt><dd>The coefficients and state variables for a filter are stored together in an instance data structure. A separate instance structure must be defined for each filter. Coefficient and offset arrays may be shared among several instances while state variable arrays cannot be shared. There are separate instance structure declarations for each of the 4 supported data types.</dd></dl>
<dl class="section user"><dt>Initialization Functions </dt><dd>There is also an associated initialization function for each data type. The initialization function performs the following operations:<ul>
<li>Sets the values of the internal structure fields.</li>
<li>Zeros out the values in the state buffer. To do this manually without calling the init function, assign the follow subfields of the instance structure: numTaps, pCoeffs, pTapDelay, maxDelay, stateIndex, pState. Also set all of the values in pState to zero.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Use of the initialization function is optional. However, if the initialization function is used, then the instance structure cannot be placed into a const data section. To place an instance structure into a const data section, the instance structure must be manually initialized. Set the values in the state buffer to zeros before static initialization. The code below statically initializes each of the 4 different data type filter instance structures <pre>    
*arm_fir_sparse_instance_f32 S = {numTaps, 0, pState, pCoeffs, maxDelay, pTapDelay};    
*arm_fir_sparse_instance_q31 S = {numTaps, 0, pState, pCoeffs, maxDelay, pTapDelay};    
*arm_fir_sparse_instance_q15 S = {numTaps, 0, pState, pCoeffs, maxDelay, pTapDelay};    
*arm_fir_sparse_instance_q7 S =  {numTaps, 0, pState, pCoeffs, maxDelay, pTapDelay};    
  </pre> </dd></dl>
<dl class="section user"><dt></dt><dd></dd></dl>
<dl class="section user"><dt>Fixed-Point Behavior </dt><dd>Care must be taken when using the fixed-point versions of the sparse FIR filter functions. In particular, the overflow and saturation behavior of the accumulator used in each function must be considered. Refer to the function specific documentation below for usage guidelines. </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga23a9284de5ee39406713b91d18ac8838"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_sparse_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__fir__sparse__instance__f32.html">arm_fir_sparse_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pScratchIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point sparse FIR structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratchIn</td><td>points to a temporary buffer of size blockSize. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of input samples to process per call. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#ae469fac5e1df35f8bcf1b3d7c3136484">arm_circularRead_f32()</a>, <a class="el" href="arm__math_8h.html#a6ff56c0896ce00712ba8f2fcf72cacd3">arm_circularWrite_f32()</a>, <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__fir__sparse__instance__f32.html#af8b8c775f4084c36774f06c082b4c078">arm_fir_sparse_instance_f32::maxDelay</a>, <a class="el" href="structarm__fir__sparse__instance__f32.html#a5e19e7f234ac30a3db843352bf2a8515">arm_fir_sparse_instance_f32::numTaps</a>, <a class="el" href="structarm__fir__sparse__instance__f32.html#a04af7c738dfb0882ad102fcad501d94a">arm_fir_sparse_instance_f32::pCoeffs</a>, <a class="el" href="structarm__fir__sparse__instance__f32.html#a794af0916666d11cc564d6df08553555">arm_fir_sparse_instance_f32::pState</a>, <a class="el" href="structarm__fir__sparse__instance__f32.html#aaa54ae67e5d10c6dd0d697945c638d31">arm_fir_sparse_instance_f32::pTapDelay</a>, and <a class="el" href="structarm__fir__sparse__instance__f32.html#a57585aeca9dc8686e08df2865375a86d">arm_fir_sparse_instance_f32::stateIndex</a>.</p>

</div>
</div>
<a class="anchor" id="ga86378a08a9d9e1e0e5de77843b34d396"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_sparse_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__fir__sparse__instance__f32.html">arm_fir_sparse_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numTaps</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t *&#160;</td>
          <td class="paramname"><em>pTapDelay</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>maxDelay</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the floating-point sparse FIR structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numTaps</td><td>number of nonzero coefficients in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the array of filter coefficients. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pTapDelay</td><td>points to the array of offset times. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">maxDelay</td><td>maximum offset time supported. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples that will be processed per block. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p><b>Description:</b> </p>
<dl class="section user"><dt></dt><dd><code>pCoeffs</code> holds the filter coefficients and has length <code>numTaps</code>. <code>pState</code> holds the filter's state variables and must be of length <code>maxDelay + blockSize</code>, where <code>maxDelay</code> is the maximum number of delay line values. <code>blockSize</code> is the number of samples processed by the <code><a class="el" href="group___f_i_r___sparse.html#ga23a9284de5ee39406713b91d18ac8838" title="Processing function for the floating-point sparse FIR filter.">arm_fir_sparse_f32()</a></code> function. </dd></dl>

<p>References <a class="el" href="structarm__fir__sparse__instance__f32.html#af8b8c775f4084c36774f06c082b4c078">arm_fir_sparse_instance_f32::maxDelay</a>, <a class="el" href="structarm__fir__sparse__instance__f32.html#a5e19e7f234ac30a3db843352bf2a8515">arm_fir_sparse_instance_f32::numTaps</a>, <a class="el" href="structarm__fir__sparse__instance__f32.html#a04af7c738dfb0882ad102fcad501d94a">arm_fir_sparse_instance_f32::pCoeffs</a>, <a class="el" href="structarm__fir__sparse__instance__f32.html#a794af0916666d11cc564d6df08553555">arm_fir_sparse_instance_f32::pState</a>, <a class="el" href="structarm__fir__sparse__instance__f32.html#aaa54ae67e5d10c6dd0d697945c638d31">arm_fir_sparse_instance_f32::pTapDelay</a>, and <a class="el" href="structarm__fir__sparse__instance__f32.html#a57585aeca9dc8686e08df2865375a86d">arm_fir_sparse_instance_f32::stateIndex</a>.</p>

</div>
</div>
<a class="anchor" id="ga5eaa80bf72bcccef5a2c5fc6648d1baa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_sparse_init_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__fir__sparse__instance__q15.html">arm_fir_sparse_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numTaps</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t *&#160;</td>
          <td class="paramname"><em>pTapDelay</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>maxDelay</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q15 sparse FIR structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numTaps</td><td>number of nonzero coefficients in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the array of filter coefficients. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pTapDelay</td><td>points to the array of offset times. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">maxDelay</td><td>maximum offset time supported. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples that will be processed per block. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p><b>Description:</b> </p>
<dl class="section user"><dt></dt><dd><code>pCoeffs</code> holds the filter coefficients and has length <code>numTaps</code>. <code>pState</code> holds the filter's state variables and must be of length <code>maxDelay + blockSize</code>, where <code>maxDelay</code> is the maximum number of delay line values. <code>blockSize</code> is the number of words processed by <code><a class="el" href="group___f_i_r___sparse.html#ga2bffda2e156e72427e19276cd9c3d3cc" title="Processing function for the Q15 sparse FIR filter.">arm_fir_sparse_q15()</a></code> function. </dd></dl>

<p>References <a class="el" href="structarm__fir__sparse__instance__q15.html#ad14cc1070eecf7e1926d8f67a8273182">arm_fir_sparse_instance_q15::maxDelay</a>, <a class="el" href="structarm__fir__sparse__instance__q15.html#a0f66b126dd8b85f7467cfb01b7bc4d77">arm_fir_sparse_instance_q15::numTaps</a>, <a class="el" href="structarm__fir__sparse__instance__q15.html#a78a6565473b5f0b8c77c3f0f58a76069">arm_fir_sparse_instance_q15::pCoeffs</a>, <a class="el" href="structarm__fir__sparse__instance__q15.html#a98b92b0f5208110129b9a67b1db90408">arm_fir_sparse_instance_q15::pState</a>, <a class="el" href="structarm__fir__sparse__instance__q15.html#aeab2855176c6efdb231a73a3672837d5">arm_fir_sparse_instance_q15::pTapDelay</a>, and <a class="el" href="structarm__fir__sparse__instance__q15.html#a89487f28cab52637426024005e478985">arm_fir_sparse_instance_q15::stateIndex</a>.</p>

</div>
</div>
<a class="anchor" id="ga9a0bb2134bc85d3e55c6be6d946ee634"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_sparse_init_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__fir__sparse__instance__q31.html">arm_fir_sparse_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numTaps</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t *&#160;</td>
          <td class="paramname"><em>pTapDelay</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>maxDelay</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q31 sparse FIR structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numTaps</td><td>number of nonzero coefficients in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the array of filter coefficients. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pTapDelay</td><td>points to the array of offset times. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">maxDelay</td><td>maximum offset time supported. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples that will be processed per block. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p><b>Description:</b> </p>
<dl class="section user"><dt></dt><dd><code>pCoeffs</code> holds the filter coefficients and has length <code>numTaps</code>. <code>pState</code> holds the filter's state variables and must be of length <code>maxDelay + blockSize</code>, where <code>maxDelay</code> is the maximum number of delay line values. <code>blockSize</code> is the number of words processed by <code><a class="el" href="group___f_i_r___sparse.html#ga03e9c2f0f35ad67d20bac66be9f920ec" title="Processing function for the Q31 sparse FIR filter.">arm_fir_sparse_q31()</a></code> function. </dd></dl>

<p>References <a class="el" href="structarm__fir__sparse__instance__q31.html#afdd3a1dc72132c854dc379154b68b674">arm_fir_sparse_instance_q31::maxDelay</a>, <a class="el" href="structarm__fir__sparse__instance__q31.html#a07b6c01e58ec6dde384719130d36b0dc">arm_fir_sparse_instance_q31::numTaps</a>, <a class="el" href="structarm__fir__sparse__instance__q31.html#a093d6227f0d1597982cd083fb126f4e0">arm_fir_sparse_instance_q31::pCoeffs</a>, <a class="el" href="structarm__fir__sparse__instance__q31.html#a830be89daa5a393b225048889aa045d1">arm_fir_sparse_instance_q31::pState</a>, <a class="el" href="structarm__fir__sparse__instance__q31.html#ab87ae457adec8f727afefaa2599fc983">arm_fir_sparse_instance_q31::pTapDelay</a>, and <a class="el" href="structarm__fir__sparse__instance__q31.html#a557ed9d477e76e4ad2019344f19f568a">arm_fir_sparse_instance_q31::stateIndex</a>.</p>

</div>
</div>
<a class="anchor" id="ga98f5c1a097d4572ce4ff3b0c58ebcdbd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_sparse_init_q7 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__fir__sparse__instance__q7.html">arm_fir_sparse_instance_q7</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numTaps</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t *&#160;</td>
          <td class="paramname"><em>pTapDelay</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>maxDelay</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q7 sparse FIR structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numTaps</td><td>number of nonzero coefficients in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the array of filter coefficients. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pTapDelay</td><td>points to the array of offset times. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">maxDelay</td><td>maximum offset time supported. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples that will be processed per block. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none</dd></dl>
<p><b>Description:</b> </p>
<dl class="section user"><dt></dt><dd><code>pCoeffs</code> holds the filter coefficients and has length <code>numTaps</code>. <code>pState</code> holds the filter's state variables and must be of length <code>maxDelay + blockSize</code>, where <code>maxDelay</code> is the maximum number of delay line values. <code>blockSize</code> is the number of samples processed by the <code><a class="el" href="group___f_i_r___sparse.html#gae86c145efc2d9ec32dc6d8c1ad2ccb3c" title="Processing function for the Q7 sparse FIR filter.">arm_fir_sparse_q7()</a></code> function. </dd></dl>

<p>References <a class="el" href="structarm__fir__sparse__instance__q7.html#af74dacc1d34c078283e50f2530eb91df">arm_fir_sparse_instance_q7::maxDelay</a>, <a class="el" href="structarm__fir__sparse__instance__q7.html#a54cdd27ca1c672b126c38763ce678b1c">arm_fir_sparse_instance_q7::numTaps</a>, <a class="el" href="structarm__fir__sparse__instance__q7.html#a3dac86f15e33553e8f3e19e0d712bae5">arm_fir_sparse_instance_q7::pCoeffs</a>, <a class="el" href="structarm__fir__sparse__instance__q7.html#a18072cf3ef3666d588f0d49512f2b28f">arm_fir_sparse_instance_q7::pState</a>, <a class="el" href="structarm__fir__sparse__instance__q7.html#ac625393c84bc0342ffdf26fc4eba1ac1">arm_fir_sparse_instance_q7::pTapDelay</a>, and <a class="el" href="structarm__fir__sparse__instance__q7.html#a2d2e65473fe3a3f2b953b4e0b60824df">arm_fir_sparse_instance_q7::stateIndex</a>.</p>

</div>
</div>
<a class="anchor" id="ga2bffda2e156e72427e19276cd9c3d3cc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_sparse_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__fir__sparse__instance__q15.html">arm_fir_sparse_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pScratchIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pScratchOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 sparse FIR structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratchIn</td><td>points to a temporary buffer of size blockSize. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratchOut</td><td>points to a temporary buffer of size blockSize. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of input samples to process per call. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function is implemented using an internal 32-bit accumulator. The 1.15 x 1.15 multiplications yield a 2.30 result and these are added to a 2.30 accumulator. Thus the full precision of the multiplications is maintained but there is only a single guard bit in the accumulator. If the accumulator result overflows it will wrap around rather than saturate. After all multiply-accumulates are performed, the 2.30 accumulator is truncated to 2.15 format and then saturated to 1.15 format. In order to avoid overflows the input signal or coefficients must be scaled down by log2(numTaps) bits. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#ad5fb134f83f2c802261f172e3dceb131">arm_circularRead_q15()</a>, <a class="el" href="arm__math_8h.html#a3ba2d215477e692def7fda46dda883ed">arm_circularWrite_q15()</a>, <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__fir__sparse__instance__q15.html#ad14cc1070eecf7e1926d8f67a8273182">arm_fir_sparse_instance_q15::maxDelay</a>, <a class="el" href="structarm__fir__sparse__instance__q15.html#a0f66b126dd8b85f7467cfb01b7bc4d77">arm_fir_sparse_instance_q15::numTaps</a>, <a class="el" href="structarm__fir__sparse__instance__q15.html#a78a6565473b5f0b8c77c3f0f58a76069">arm_fir_sparse_instance_q15::pCoeffs</a>, <a class="el" href="structarm__fir__sparse__instance__q15.html#a98b92b0f5208110129b9a67b1db90408">arm_fir_sparse_instance_q15::pState</a>, <a class="el" href="structarm__fir__sparse__instance__q15.html#aeab2855176c6efdb231a73a3672837d5">arm_fir_sparse_instance_q15::pTapDelay</a>, and <a class="el" href="structarm__fir__sparse__instance__q15.html#a89487f28cab52637426024005e478985">arm_fir_sparse_instance_q15::stateIndex</a>.</p>

</div>
</div>
<a class="anchor" id="ga03e9c2f0f35ad67d20bac66be9f920ec"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_sparse_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__fir__sparse__instance__q31.html">arm_fir_sparse_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pScratchIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q31 sparse FIR structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratchIn</td><td>points to a temporary buffer of size blockSize. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of input samples to process per call. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function is implemented using an internal 32-bit accumulator. The 1.31 x 1.31 multiplications are truncated to 2.30 format. This leads to loss of precision on the intermediate multiplications and provides only a single guard bit. If the accumulator result overflows, it wraps around rather than saturate. In order to avoid overflows the input signal or coefficients must be scaled down by log2(numTaps) bits. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#ae469fac5e1df35f8bcf1b3d7c3136484">arm_circularRead_f32()</a>, <a class="el" href="arm__math_8h.html#a6ff56c0896ce00712ba8f2fcf72cacd3">arm_circularWrite_f32()</a>, <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__fir__sparse__instance__q31.html#afdd3a1dc72132c854dc379154b68b674">arm_fir_sparse_instance_q31::maxDelay</a>, <a class="el" href="structarm__fir__sparse__instance__q31.html#a07b6c01e58ec6dde384719130d36b0dc">arm_fir_sparse_instance_q31::numTaps</a>, <a class="el" href="structarm__fir__sparse__instance__q31.html#a093d6227f0d1597982cd083fb126f4e0">arm_fir_sparse_instance_q31::pCoeffs</a>, <a class="el" href="structarm__fir__sparse__instance__q31.html#a830be89daa5a393b225048889aa045d1">arm_fir_sparse_instance_q31::pState</a>, <a class="el" href="structarm__fir__sparse__instance__q31.html#ab87ae457adec8f727afefaa2599fc983">arm_fir_sparse_instance_q31::pTapDelay</a>, and <a class="el" href="structarm__fir__sparse__instance__q31.html#a557ed9d477e76e4ad2019344f19f568a">arm_fir_sparse_instance_q31::stateIndex</a>.</p>

</div>
</div>
<a class="anchor" id="gae86c145efc2d9ec32dc6d8c1ad2ccb3c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_sparse_q7 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__fir__sparse__instance__q7.html">arm_fir_sparse_instance_q7</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pScratchIn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pScratchOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q7 sparse FIR structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratchIn</td><td>points to a temporary buffer of size blockSize. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pScratchOut</td><td>points to a temporary buffer of size blockSize. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of input samples to process per call. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function is implemented using a 32-bit internal accumulator. Both coefficients and state variables are represented in 1.7 format and multiplications yield a 2.14 result. The 2.14 intermediate results are accumulated in a 32-bit accumulator in 18.14 format. There is no risk of internal overflow with this approach and the full precision of intermediate multiplications is preserved. The accumulator is then converted to 18.7 format by discarding the low 7 bits. Finally, the result is truncated to 1.7 format. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a3ebff224ad44c217fde9f530342e2960">__PACKq7</a>, <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#a30aa80ea20abe71f3afa99f2f0391ed5">arm_circularRead_q7()</a>, <a class="el" href="arm__math_8h.html#addba85b1f7fbd472fd00ddd9ce43aea8">arm_circularWrite_q7()</a>, <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__fir__sparse__instance__q7.html#af74dacc1d34c078283e50f2530eb91df">arm_fir_sparse_instance_q7::maxDelay</a>, <a class="el" href="structarm__fir__sparse__instance__q7.html#a54cdd27ca1c672b126c38763ce678b1c">arm_fir_sparse_instance_q7::numTaps</a>, <a class="el" href="structarm__fir__sparse__instance__q7.html#a3dac86f15e33553e8f3e19e0d712bae5">arm_fir_sparse_instance_q7::pCoeffs</a>, <a class="el" href="structarm__fir__sparse__instance__q7.html#a18072cf3ef3666d588f0d49512f2b28f">arm_fir_sparse_instance_q7::pState</a>, <a class="el" href="structarm__fir__sparse__instance__q7.html#ac625393c84bc0342ffdf26fc4eba1ac1">arm_fir_sparse_instance_q7::pTapDelay</a>, and <a class="el" href="structarm__fir__sparse__instance__q7.html#a2d2e65473fe3a3f2b953b4e0b60824df">arm_fir_sparse_instance_q7::stateIndex</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
