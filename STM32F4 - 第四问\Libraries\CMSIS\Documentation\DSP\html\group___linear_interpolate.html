<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Linear Interpolation</title>
<title>CMSIS-DSP: Linear Interpolation</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___linear_interpolate.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Linear Interpolation</div>  </div>
<div class="ingroups"><a class="el" href="group__group_interpolation.html">Interpolation Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga2269263d810cafcd19681957b37d5cf6"><td class="memItemLeft" align="right" valign="top">static __INLINE <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___linear_interpolate.html#ga2269263d810cafcd19681957b37d5cf6">arm_linear_interp_f32</a> (<a class="el" href="structarm__linear__interp__instance__f32.html">arm_linear_interp_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> x)</td></tr>
<tr class="memdesc:ga2269263d810cafcd19681957b37d5cf6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Process function for the floating-point Linear Interpolation Function.  <a href="#ga2269263d810cafcd19681957b37d5cf6"></a><br/></td></tr>
<tr class="separator:ga2269263d810cafcd19681957b37d5cf6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga690e63e9a513ca0a741b1b174805d031"><td class="memItemLeft" align="right" valign="top">static __INLINE <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___linear_interpolate.html#ga690e63e9a513ca0a741b1b174805d031">arm_linear_interp_q31</a> (<a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pYData, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> x, uint32_t nValues)</td></tr>
<tr class="memdesc:ga690e63e9a513ca0a741b1b174805d031"><td class="mdescLeft">&#160;</td><td class="mdescRight">Process function for the Q31 Linear Interpolation Function.  <a href="#ga690e63e9a513ca0a741b1b174805d031"></a><br/></td></tr>
<tr class="separator:ga690e63e9a513ca0a741b1b174805d031"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga42c9206e5d2d22b8808716dc30622846"><td class="memItemLeft" align="right" valign="top">static __INLINE <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___linear_interpolate.html#ga42c9206e5d2d22b8808716dc30622846">arm_linear_interp_q15</a> (<a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pYData, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> x, uint32_t nValues)</td></tr>
<tr class="memdesc:ga42c9206e5d2d22b8808716dc30622846"><td class="mdescLeft">&#160;</td><td class="mdescRight">Process function for the Q15 Linear Interpolation Function.  <a href="#ga42c9206e5d2d22b8808716dc30622846"></a><br/></td></tr>
<tr class="separator:ga42c9206e5d2d22b8808716dc30622846"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacb0d44fe00aca0ba1d036d469a1763fc"><td class="memItemLeft" align="right" valign="top">static __INLINE <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___linear_interpolate.html#gacb0d44fe00aca0ba1d036d469a1763fc">arm_linear_interp_q7</a> (<a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *pYData, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> x, uint32_t nValues)</td></tr>
<tr class="memdesc:gacb0d44fe00aca0ba1d036d469a1763fc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Process function for the Q7 Linear Interpolation Function.  <a href="#gacb0d44fe00aca0ba1d036d469a1763fc"></a><br/></td></tr>
<tr class="separator:gacb0d44fe00aca0ba1d036d469a1763fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Linear interpolation is a method of curve fitting using linear polynomials. Linear interpolation works by effectively drawing a straight line between two neighboring samples and returning the appropriate point along that line</p>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="LinearInterp.gif" alt="LinearInterp.gif"/>
<div class="caption">
Linear interpolation</div></div>
 </dd></dl>
<dl class="section user"><dt></dt><dd>A Linear Interpolate function calculates an output value(y), for the input(x) using linear interpolation of the input values x0, x1( nearest input values) and the output values y0 and y1(nearest output values)</dd></dl>
<dl class="section user"><dt>Algorithm:</dt><dd><pre>
      y = y0 + (x - x0) * ((y1 - y0)/(x1-x0))
      where x0, x1 are nearest values of input x
            y0, y1 are nearest values to output y
</pre></dd></dl>
<dl class="section user"><dt></dt><dd>This set of functions implements Linear interpolation process for Q7, Q15, Q31, and floating-point data types. The functions operate on a single sample of data and each call to the function returns a single processed value. <code>S</code> points to an instance of the Linear Interpolate function data structure. <code>x</code> is the input sample value. The functions returns the output value.</dd></dl>
<dl class="section user"><dt></dt><dd>if x is outside of the table boundary, Linear interpolation returns first value of the table if x is below input range and returns last value of table if x is above range. </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga2269263d810cafcd19681957b37d5cf6"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static __INLINE <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> arm_linear_interp_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__linear__interp__instance__f32.html">arm_linear_interp_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td>
          <td class="paramname"><em>x</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>is an instance of the floating-point Linear Interpolation structure </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">x</td><td>input sample to process </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>y processed output sample. </dd></dl>
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_linear_interp_example_f32_8c-example.html#a13">arm_linear_interp_example_f32.c</a>.</dd>
</dl>
<p>References <a class="el" href="structarm__linear__interp__instance__f32.html#a95f02a926b16d35359aca5b31e813b11">arm_linear_interp_instance_f32::nValues</a>, <a class="el" href="structarm__linear__interp__instance__f32.html#ab373001f6afad0850359c344a4d7eee4">arm_linear_interp_instance_f32::pYData</a>, <a class="el" href="structarm__linear__interp__instance__f32.html#a08352dc6ea82fbc0827408e018535481">arm_linear_interp_instance_f32::x1</a>, and <a class="el" href="structarm__linear__interp__instance__f32.html#aa8e2d686b5434a406d390b347b183511">arm_linear_interp_instance_f32::xSpacing</a>.</p>

<p>Referenced by <a class="el" href="arm__linear__interp__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="ga42c9206e5d2d22b8808716dc30622846"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static __INLINE <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> arm_linear_interp_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pYData</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>nValues</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pYData</td><td>pointer to Q15 Linear Interpolation table </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">x</td><td>input sample to process </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">nValues</td><td>number of table values </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>y processed output sample.</dd></dl>
<dl class="section user"><dt></dt><dd>Input sample <code>x</code> is in 12.20 format which contains 12 bits for table index and 20 bits for fractional part. This function can support maximum of table size 2^12. </dd></dl>

</div>
</div>
<a class="anchor" id="ga690e63e9a513ca0a741b1b174805d031"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static __INLINE <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> arm_linear_interp_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pYData</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>nValues</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pYData</td><td>pointer to Q31 Linear Interpolation table </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">x</td><td>input sample to process </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">nValues</td><td>number of table values </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>y processed output sample.</dd></dl>
<dl class="section user"><dt></dt><dd>Input sample <code>x</code> is in 12.20 format which contains 12 bits for table index and 20 bits for fractional part. This function can support maximum of table size 2^12. </dd></dl>

</div>
</div>
<a class="anchor" id="gacb0d44fe00aca0ba1d036d469a1763fc"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static __INLINE <a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> arm_linear_interp_q7 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ae541b6f232c305361e9b416fc9eed263">q7_t</a> *&#160;</td>
          <td class="paramname"><em>pYData</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>nValues</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*pYData</td><td>pointer to Q7 Linear Interpolation table </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">x</td><td>input sample to process </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">nValues</td><td>number of table values </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>y processed output sample.</dd></dl>
<dl class="section user"><dt></dt><dd>Input sample <code>x</code> is in 12.20 format which contains 12 bits for table index and 20 bits for fractional part. This function can support maximum of table size 2^12. </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
