<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Normalized LMS Filters</title>
<title>CMSIS-DSP: Normalized LMS Filters</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___l_m_s___n_o_r_m.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Normalized LMS Filters</div>  </div>
<div class="ingroups"><a class="el" href="group__group_filters.html">Filtering Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga2418c929087c6eba719758eaae3f3300"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___l_m_s___n_o_r_m.html#ga2418c929087c6eba719758eaae3f3300">arm_lms_norm_f32</a> (<a class="el" href="structarm__lms__norm__instance__f32.html">arm_lms_norm_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrc, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pRef, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pOut, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pErr, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga2418c929087c6eba719758eaae3f3300"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for floating-point normalized LMS filter.  <a href="#ga2418c929087c6eba719758eaae3f3300"></a><br/></td></tr>
<tr class="separator:ga2418c929087c6eba719758eaae3f3300"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac7ccbaea863882056eee815456464670"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___l_m_s___n_o_r_m.html#gac7ccbaea863882056eee815456464670">arm_lms_norm_init_f32</a> (<a class="el" href="structarm__lms__norm__instance__f32.html">arm_lms_norm_instance_f32</a> *S, uint16_t numTaps, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pState, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> mu, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gac7ccbaea863882056eee815456464670"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for floating-point normalized LMS filter.  <a href="#gac7ccbaea863882056eee815456464670"></a><br/></td></tr>
<tr class="separator:gac7ccbaea863882056eee815456464670"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga213ab1ee2e154cc2fa30d667b1994b89"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___l_m_s___n_o_r_m.html#ga213ab1ee2e154cc2fa30d667b1994b89">arm_lms_norm_init_q15</a> (<a class="el" href="structarm__lms__norm__instance__q15.html">arm_lms_norm_instance_q15</a> *S, uint16_t numTaps, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pState, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> mu, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, uint8_t postShift)</td></tr>
<tr class="memdesc:ga213ab1ee2e154cc2fa30d667b1994b89"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for Q15 normalized LMS filter.  <a href="#ga213ab1ee2e154cc2fa30d667b1994b89"></a><br/></td></tr>
<tr class="separator:ga213ab1ee2e154cc2fa30d667b1994b89"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1d9659dbbea4c89a7a9d14d5fc0dd490"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___l_m_s___n_o_r_m.html#ga1d9659dbbea4c89a7a9d14d5fc0dd490">arm_lms_norm_init_q31</a> (<a class="el" href="structarm__lms__norm__instance__q31.html">arm_lms_norm_instance_q31</a> *S, uint16_t numTaps, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pState, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> mu, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, uint8_t postShift)</td></tr>
<tr class="memdesc:ga1d9659dbbea4c89a7a9d14d5fc0dd490"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for Q31 normalized LMS filter.  <a href="#ga1d9659dbbea4c89a7a9d14d5fc0dd490"></a><br/></td></tr>
<tr class="separator:ga1d9659dbbea4c89a7a9d14d5fc0dd490"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad47486a399dedb0bc85a5990ec5cf981"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___l_m_s___n_o_r_m.html#gad47486a399dedb0bc85a5990ec5cf981">arm_lms_norm_q15</a> (<a class="el" href="structarm__lms__norm__instance__q15.html">arm_lms_norm_instance_q15</a> *S, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrc, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pRef, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pOut, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pErr, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gad47486a399dedb0bc85a5990ec5cf981"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for Q15 normalized LMS filter.  <a href="#gad47486a399dedb0bc85a5990ec5cf981"></a><br/></td></tr>
<tr class="separator:gad47486a399dedb0bc85a5990ec5cf981"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7128775e99817c183a7d7ad34e8b6e05"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05">arm_lms_norm_q31</a> (<a class="el" href="structarm__lms__norm__instance__q31.html">arm_lms_norm_instance_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pRef, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pOut, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pErr, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga7128775e99817c183a7d7ad34e8b6e05"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for Q31 normalized LMS filter.  <a href="#ga7128775e99817c183a7d7ad34e8b6e05"></a><br/></td></tr>
<tr class="separator:ga7128775e99817c183a7d7ad34e8b6e05"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>This set of functions implements a commonly used adaptive filter. It is related to the Least Mean Square (LMS) adaptive filter and includes an additional normalization factor which increases the adaptation rate of the filter. The CMSIS DSP Library contains normalized LMS filter functions that operate on Q15, Q31, and floating-point data types.</p>
<p>A normalized least mean square (NLMS) filter consists of two components as shown below. The first component is a standard transversal or FIR filter. The second component is a coefficient update mechanism. The NLMS filter has two input signals. The "input" feeds the FIR filter while the "reference input" corresponds to the desired output of the FIR filter. That is, the FIR filter coefficients are updated so that the output of the FIR filter matches the reference input. The filter coefficient update mechanism is based on the difference between the FIR filter output and the reference input. This "error signal" tends towards zero as the filter adapts. The NLMS processing functions accept the input and reference input signals and generate the filter output and error signal. </p>
<div class="image">
<img src="LMS.gif" alt="LMS.gif"/>
<div class="caption">
Internal structure of the NLMS adaptive filter</div></div>
<p>The functions operate on blocks of data and each call to the function processes <code>blockSize</code> samples through the filter. <code>pSrc</code> points to input signal, <code>pRef</code> points to reference signal, <code>pOut</code> points to output signal and <code>pErr</code> points to error signal. All arrays contain <code>blockSize</code> values.</p>
<p>The functions operate on a block-by-block basis. Internally, the filter coefficients <code>b[n]</code> are updated on a sample-by-sample basis. The convergence of the LMS filter is slower compared to the normalized LMS algorithm.</p>
<dl class="section user"><dt>Algorithm: </dt><dd>The output signal <code>y[n]</code> is computed by a standard FIR filter: <pre>    
     y[n] = b[0] * x[n] + b[1] * x[n-1] + b[2] * x[n-2] + ...+ b[numTaps-1] * x[n-numTaps+1]    
 </pre></dd></dl>
<dl class="section user"><dt></dt><dd>The error signal equals the difference between the reference signal <code>d[n]</code> and the filter output: <pre>    
     e[n] = d[n] - y[n].    
 </pre></dd></dl>
<dl class="section user"><dt></dt><dd>After each sample of the error signal is computed the instanteous energy of the filter state variables is calculated: <pre>    
    E = x[n]^2 + x[n-1]^2 + ... + x[n-numTaps+1]^2.    
 </pre> The filter coefficients <code>b[k]</code> are then updated on a sample-by-sample basis: <pre>    
     b[k] = b[k] + e[n] * (mu/E) * x[n-k],  for k=0, 1, ..., numTaps-1    
 </pre> where <code>mu</code> is the step size and controls the rate of coefficient convergence. </dd></dl>
<dl class="section user"><dt></dt><dd>In the APIs, <code>pCoeffs</code> points to a coefficient array of size <code>numTaps</code>. Coefficients are stored in time reversed order. </dd></dl>
<dl class="section user"><dt></dt><dd><pre>    
    {b[numTaps-1], b[numTaps-2], b[N-2], ..., b[1], b[0]}    
 </pre> </dd></dl>
<dl class="section user"><dt></dt><dd><code>pState</code> points to a state array of size <code>numTaps + blockSize - 1</code>. Samples in the state buffer are stored in the order: </dd></dl>
<dl class="section user"><dt></dt><dd><pre>    
    {x[n-numTaps+1], x[n-numTaps], x[n-numTaps-1], x[n-numTaps-2]....x[0], x[1], ..., x[blockSize-1]}    
 </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>Note that the length of the state buffer exceeds the length of the coefficient array by <code>blockSize-1</code> samples. The increased state buffer length allows circular addressing, which is traditionally used in FIR filters, to be avoided and yields a significant speed improvement. The state variables are updated after each block of data is processed. </dd></dl>
<dl class="section user"><dt>Instance Structure </dt><dd>The coefficients and state variables for a filter are stored together in an instance data structure. A separate instance structure must be defined for each filter and coefficient and state arrays cannot be shared among instances. There are separate instance structure declarations for each of the 3 supported data types.</dd></dl>
<dl class="section user"><dt>Initialization Functions </dt><dd>There is also an associated initialization function for each data type. The initialization function performs the following operations:<ul>
<li>Sets the values of the internal structure fields.</li>
<li>Zeros out the values in the state buffer. To do this manually without calling the init function, assign the follow subfields of the instance structure: numTaps, pCoeffs, mu, energy, x0, pState. Also set all of the values in pState to zero. For Q7, Q15, and Q31 the following fields must also be initialized; recipTable, postShift</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Instance structure cannot be placed into a const data section and it is recommended to use the initialization function. </dd></dl>
<dl class="section user"><dt>Fixed-Point Behavior: </dt><dd>Care must be taken when using the Q15 and Q31 versions of the normalised LMS filter. The following issues must be considered:<ul>
<li>Scaling of coefficients</li>
<li>Overflow and saturation</li>
</ul>
</dd></dl>
<dl class="section user"><dt>Scaling of Coefficients: </dt><dd>Filter coefficients are represented as fractional values and coefficients are restricted to lie in the range <code>[-1 +1)</code>. The fixed-point functions have an additional scaling parameter <code>postShift</code>. At the output of the filter's accumulator is a shift register which shifts the result by <code>postShift</code> bits. This essentially scales the filter coefficients by <code>2^postShift</code> and allows the filter coefficients to exceed the range <code>[+1 -1)</code>. The value of <code>postShift</code> is set by the user based on the expected gain through the system being modeled.</dd></dl>
<dl class="section user"><dt>Overflow and Saturation: </dt><dd>Overflow and saturation behavior of the fixed-point Q15 and Q31 versions are described separately as part of the function specific documentation below. </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga2418c929087c6eba719758eaae3f3300"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_lms_norm_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__lms__norm__instance__f32.html">arm_lms_norm_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pRef</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pErr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point normalized LMS filter structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pRef</td><td>points to the block of reference data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pOut</td><td>points to the block of output data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pErr</td><td>points to the block of error data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a29">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
<p>References <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__lms__norm__instance__f32.html#a6a4119e4f39447bbee31b066deafa16f">arm_lms_norm_instance_f32::energy</a>, <a class="el" href="structarm__lms__norm__instance__f32.html#a84401d3cfc6c40f69c08223cf341b886">arm_lms_norm_instance_f32::mu</a>, <a class="el" href="structarm__lms__norm__instance__f32.html#ac95f8ca3d816524c2070643852fac5e8">arm_lms_norm_instance_f32::numTaps</a>, <a class="el" href="structarm__lms__norm__instance__f32.html#a1ba688d90aba7de003ed4ad8e2e7ddda">arm_lms_norm_instance_f32::pCoeffs</a>, <a class="el" href="structarm__lms__norm__instance__f32.html#a0bc03338687002ed5f2e4a363eb095ec">arm_lms_norm_instance_f32::pState</a>, and <a class="el" href="structarm__lms__norm__instance__f32.html#aec958fe89b164a30f38bcca9f5d96218">arm_lms_norm_instance_f32::x0</a>.</p>

<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="gac7ccbaea863882056eee815456464670"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_lms_norm_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__lms__norm__instance__f32.html">arm_lms_norm_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numTaps</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td>
          <td class="paramname"><em>mu</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point LMS filter structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numTaps</td><td>number of filter coefficients. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to coefficient buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to state buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mu</td><td>step size that controls filter coefficient updates. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<dl class="section user"><dt>Description: </dt><dd><code>pCoeffs</code> points to the array of filter coefficients stored in time reversed order: <pre>    
   {b[numTaps-1], b[numTaps-2], b[N-2], ..., b[1], b[0]}    
</pre> The initial filter coefficients serve as a starting point for the adaptive filter. <code>pState</code> points to an array of length <code>numTaps+blockSize-1</code> samples, where <code>blockSize</code> is the number of input samples processed by each call to <code><a class="el" href="group___l_m_s___n_o_r_m.html#ga2418c929087c6eba719758eaae3f3300" title="Processing function for floating-point normalized LMS filter.">arm_lms_norm_f32()</a></code>. </dd></dl>
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_signal_converge_example_f32_8c-example.html#a23">arm_signal_converge_example_f32.c</a>.</dd>
</dl>
<p>References <a class="el" href="structarm__lms__norm__instance__f32.html#a6a4119e4f39447bbee31b066deafa16f">arm_lms_norm_instance_f32::energy</a>, <a class="el" href="structarm__lms__norm__instance__f32.html#a84401d3cfc6c40f69c08223cf341b886">arm_lms_norm_instance_f32::mu</a>, <a class="el" href="structarm__lms__norm__instance__f32.html#ac95f8ca3d816524c2070643852fac5e8">arm_lms_norm_instance_f32::numTaps</a>, <a class="el" href="structarm__lms__norm__instance__f32.html#a1ba688d90aba7de003ed4ad8e2e7ddda">arm_lms_norm_instance_f32::pCoeffs</a>, <a class="el" href="structarm__lms__norm__instance__f32.html#a0bc03338687002ed5f2e4a363eb095ec">arm_lms_norm_instance_f32::pState</a>, and <a class="el" href="structarm__lms__norm__instance__f32.html#aec958fe89b164a30f38bcca9f5d96218">arm_lms_norm_instance_f32::x0</a>.</p>

<p>Referenced by <a class="el" href="arm__signal__converge__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="ga213ab1ee2e154cc2fa30d667b1994b89"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_lms_norm_init_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__lms__norm__instance__q15.html">arm_lms_norm_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numTaps</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td>
          <td class="paramname"><em>mu</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>postShift</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 normalized LMS filter structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numTaps</td><td>number of filter coefficients. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to coefficient buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to state buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mu</td><td>step size that controls filter coefficient updates. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">postShift</td><td>bit shift applied to coefficients. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Description:</b> </p>
<dl class="section user"><dt></dt><dd><code>pCoeffs</code> points to the array of filter coefficients stored in time reversed order: <pre>    
   {b[numTaps-1], b[numTaps-2], b[N-2], ..., b[1], b[0]}    
</pre> The initial filter coefficients serve as a starting point for the adaptive filter. <code>pState</code> points to the array of state variables and size of array is <code>numTaps+blockSize-1</code> samples, where <code>blockSize</code> is the number of input samples processed by each call to <code><a class="el" href="group___l_m_s___n_o_r_m.html#gad47486a399dedb0bc85a5990ec5cf981" title="Processing function for Q15 normalized LMS filter.">arm_lms_norm_q15()</a></code>. </dd></dl>

<p>References <a class="el" href="arm__common__tables_8c.html#a66ca8ac5f3a63d9962f501ae60aa32be">armRecipTableQ15</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#a1c81ded399919d8181026bc1c8602e7b">arm_lms_norm_instance_q15::energy</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#a7ce00f21d11cfda6d963240641deea8c">arm_lms_norm_instance_q15::mu</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#a9ee7a45f4f315d7996a969e25fdc7146">arm_lms_norm_instance_q15::numTaps</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#ae7bca648c75a2ffa02d87852bb78bc8a">arm_lms_norm_instance_q15::pCoeffs</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#aa0d435fbcf7dedb7179d4467e9b79e9f">arm_lms_norm_instance_q15::postShift</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#aa4de490b3bdbd03561b76ee07901c8e3">arm_lms_norm_instance_q15::pState</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#a9aabb0e4c79f3db807e7a441fa36f5f8">arm_lms_norm_instance_q15::recipTable</a>, and <a class="el" href="structarm__lms__norm__instance__q15.html#a3fc1d6f97d2c6d5324871de6895cb7e9">arm_lms_norm_instance_q15::x0</a>.</p>

</div>
</div>
<a class="anchor" id="ga1d9659dbbea4c89a7a9d14d5fc0dd490"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_lms_norm_init_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__lms__norm__instance__q31.html">arm_lms_norm_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numTaps</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td>
          <td class="paramname"><em>mu</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>postShift</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q31 normalized LMS filter structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numTaps</td><td>number of filter coefficients. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to coefficient buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to state buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mu</td><td>step size that controls filter coefficient updates. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">postShift</td><td>bit shift applied to coefficients. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Description:</b> </p>
<dl class="section user"><dt></dt><dd><code>pCoeffs</code> points to the array of filter coefficients stored in time reversed order: <pre>    
   {b[numTaps-1], b[numTaps-2], b[N-2], ..., b[1], b[0]}    
</pre> The initial filter coefficients serve as a starting point for the adaptive filter. <code>pState</code> points to an array of length <code>numTaps+blockSize-1</code> samples, where <code>blockSize</code> is the number of input samples processed by each call to <code><a class="el" href="group___l_m_s___n_o_r_m.html#ga7128775e99817c183a7d7ad34e8b6e05" title="Processing function for Q31 normalized LMS filter.">arm_lms_norm_q31()</a></code>. </dd></dl>

<p>References <a class="el" href="arm__common__tables_8c.html#aae6056f6c4e8f7e494445196bf864479">armRecipTableQ31</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#a3c0ae42869afec8555dc8e3a7ef9b386">arm_lms_norm_instance_q31::energy</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#ad3dd2a2406e02fdaa7782ba6c3940a64">arm_lms_norm_instance_q31::mu</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#a28e4c085af69c9c3e2e95dacf8004c3e">arm_lms_norm_instance_q31::numTaps</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#a57a64c1ff102d033c1bd05043f1d9955">arm_lms_norm_instance_q31::pCoeffs</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#a28d7b9e437817f83397e081967e90f3c">arm_lms_norm_instance_q31::postShift</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#a6b25c96cf048b77078d62f4252a01ec4">arm_lms_norm_instance_q31::pState</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#a85836d0907077b9ac660f7bbbaa9d694">arm_lms_norm_instance_q31::recipTable</a>, and <a class="el" href="structarm__lms__norm__instance__q31.html#a47c4466d644e0d8ba407995adfa9b917">arm_lms_norm_instance_q31::x0</a>.</p>

</div>
</div>
<a class="anchor" id="gad47486a399dedb0bc85a5990ec5cf981"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_lms_norm_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__lms__norm__instance__q15.html">arm_lms_norm_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pRef</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pErr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 normalized LMS filter structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pRef</td><td>points to the block of reference data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pOut</td><td>points to the block of output data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pErr</td><td>points to the block of error data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function is implemented using a 64-bit internal accumulator. Both coefficients and state variables are represented in 1.15 format and multiplications yield a 2.30 result. The 2.30 intermediate results are accumulated in a 64-bit accumulator in 34.30 format. There is no risk of internal overflow with this approach and the full precision of intermediate multiplications is preserved. After all additions have been performed, the accumulator is truncated to 34.15 format by discarding low 15 bits. Lastly, the accumulator is saturated to yield a result in 1.15 format.</dd></dl>
<dl class="section user"><dt></dt><dd>In this filter, filter coefficients are updated for each sample and the updation of filter cofficients are saturted. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__math_8h.html#a1c66e370a6ae91aaafbaec5e979198d7">arm_recip_q15()</a>, <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="arm__math_8h.html#a663277ff19ad0b409fb98b64b2c2750b">DELTA_Q15</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#a1c81ded399919d8181026bc1c8602e7b">arm_lms_norm_instance_q15::energy</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#a7ce00f21d11cfda6d963240641deea8c">arm_lms_norm_instance_q15::mu</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#a9ee7a45f4f315d7996a969e25fdc7146">arm_lms_norm_instance_q15::numTaps</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#ae7bca648c75a2ffa02d87852bb78bc8a">arm_lms_norm_instance_q15::pCoeffs</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#aa0d435fbcf7dedb7179d4467e9b79e9f">arm_lms_norm_instance_q15::postShift</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#aa4de490b3bdbd03561b76ee07901c8e3">arm_lms_norm_instance_q15::pState</a>, <a class="el" href="structarm__lms__norm__instance__q15.html#a9aabb0e4c79f3db807e7a441fa36f5f8">arm_lms_norm_instance_q15::recipTable</a>, and <a class="el" href="structarm__lms__norm__instance__q15.html#a3fc1d6f97d2c6d5324871de6895cb7e9">arm_lms_norm_instance_q15::x0</a>.</p>

</div>
</div>
<a class="anchor" id="ga7128775e99817c183a7d7ad34e8b6e05"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_lms_norm_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__lms__norm__instance__q31.html">arm_lms_norm_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pRef</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pErr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q31 normalized LMS filter structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pRef</td><td>points to the block of reference data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pOut</td><td>points to the block of output data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pErr</td><td>points to the block of error data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function is implemented using an internal 64-bit accumulator. The accumulator has a 2.62 format and maintains full precision of the intermediate multiplication results but provides only a single guard bit. Thus, if the accumulator result overflows it wraps around rather than clip. In order to avoid overflows completely the input signal must be scaled down by log2(numTaps) bits. The reference signal should not be scaled down. After all multiply-accumulates are performed, the 2.62 accumulator is shifted and saturated to 1.31 format to yield the final result. The output signal and error signal are in 1.31 format.</dd></dl>
<dl class="section user"><dt></dt><dd>In this filter, filter coefficients are updated for each sample and the updation of filter cofficients are saturted. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a43140f04ca94c2a7394e7a222e2d8fb4">arm_recip_q31()</a>, <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="arm__math_8h.html#ad7373e53d3c2e1adfeafc8c2e9720b5c">clip_q63_to_q31()</a>, <a class="el" href="arm__math_8h.html#aad77ae594e95c5af6ae4129bd6a483c2">DELTA_Q31</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#a3c0ae42869afec8555dc8e3a7ef9b386">arm_lms_norm_instance_q31::energy</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#ad3dd2a2406e02fdaa7782ba6c3940a64">arm_lms_norm_instance_q31::mu</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#a28e4c085af69c9c3e2e95dacf8004c3e">arm_lms_norm_instance_q31::numTaps</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#a57a64c1ff102d033c1bd05043f1d9955">arm_lms_norm_instance_q31::pCoeffs</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#a28d7b9e437817f83397e081967e90f3c">arm_lms_norm_instance_q31::postShift</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#a6b25c96cf048b77078d62f4252a01ec4">arm_lms_norm_instance_q31::pState</a>, <a class="el" href="structarm__lms__norm__instance__q31.html#a85836d0907077b9ac660f7bbbaa9d694">arm_lms_norm_instance_q31::recipTable</a>, and <a class="el" href="structarm__lms__norm__instance__q31.html#a47c4466d644e0d8ba407995adfa9b917">arm_lms_norm_instance_q31::x0</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
