<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Finite Impulse Response (FIR) Lattice Filters</title>
<title>CMSIS-DSP: Finite Impulse Response (FIR) Lattice Filters</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___f_i_r___lattice.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Finite Impulse Response (FIR) Lattice Filters</div>  </div>
<div class="ingroups"><a class="el" href="group__group_filters.html">Filtering Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gae63a45a63a11a65f2eae8b8b1fe370a8"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___lattice.html#gae63a45a63a11a65f2eae8b8b1fe370a8">arm_fir_lattice_f32</a> (const <a class="el" href="structarm__fir__lattice__instance__f32.html">arm_fir_lattice_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrc, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gae63a45a63a11a65f2eae8b8b1fe370a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the floating-point FIR lattice filter.  <a href="#gae63a45a63a11a65f2eae8b8b1fe370a8"></a><br/></td></tr>
<tr class="separator:gae63a45a63a11a65f2eae8b8b1fe370a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga86199a1590af2b8941c6532ee9d03229"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___lattice.html#ga86199a1590af2b8941c6532ee9d03229">arm_fir_lattice_init_f32</a> (<a class="el" href="structarm__fir__lattice__instance__f32.html">arm_fir_lattice_instance_f32</a> *S, uint16_t numStages, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pState)</td></tr>
<tr class="memdesc:ga86199a1590af2b8941c6532ee9d03229"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point FIR lattice filter.  <a href="#ga86199a1590af2b8941c6532ee9d03229"></a><br/></td></tr>
<tr class="separator:ga86199a1590af2b8941c6532ee9d03229"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1b22f30ce1cc19bf5a5d7c9fca154d72"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___lattice.html#ga1b22f30ce1cc19bf5a5d7c9fca154d72">arm_fir_lattice_init_q15</a> (<a class="el" href="structarm__fir__lattice__instance__q15.html">arm_fir_lattice_instance_q15</a> *S, uint16_t numStages, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pState)</td></tr>
<tr class="memdesc:ga1b22f30ce1cc19bf5a5d7c9fca154d72"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q15 FIR lattice filter.  <a href="#ga1b22f30ce1cc19bf5a5d7c9fca154d72"></a><br/></td></tr>
<tr class="separator:ga1b22f30ce1cc19bf5a5d7c9fca154d72"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac05a17a0188bb851b58d19e572870a54"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___lattice.html#gac05a17a0188bb851b58d19e572870a54">arm_fir_lattice_init_q31</a> (<a class="el" href="structarm__fir__lattice__instance__q31.html">arm_fir_lattice_instance_q31</a> *S, uint16_t numStages, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pCoeffs, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pState)</td></tr>
<tr class="memdesc:gac05a17a0188bb851b58d19e572870a54"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q31 FIR lattice filter.  <a href="#gac05a17a0188bb851b58d19e572870a54"></a><br/></td></tr>
<tr class="separator:gac05a17a0188bb851b58d19e572870a54"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabb0ab07fd313b4d863070c3ddca51542"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___lattice.html#gabb0ab07fd313b4d863070c3ddca51542">arm_fir_lattice_q15</a> (const <a class="el" href="structarm__fir__lattice__instance__q15.html">arm_fir_lattice_instance_q15</a> *S, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrc, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gabb0ab07fd313b4d863070c3ddca51542"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q15 FIR lattice filter.  <a href="#gabb0ab07fd313b4d863070c3ddca51542"></a><br/></td></tr>
<tr class="separator:gabb0ab07fd313b4d863070c3ddca51542"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2e36fd210e4a1a5dd333ce80dd6d9a88"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___f_i_r___lattice.html#ga2e36fd210e4a1a5dd333ce80dd6d9a88">arm_fir_lattice_q31</a> (const <a class="el" href="structarm__fir__lattice__instance__q31.html">arm_fir_lattice_instance_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga2e36fd210e4a1a5dd333ce80dd6d9a88"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q31 FIR lattice filter.  <a href="#ga2e36fd210e4a1a5dd333ce80dd6d9a88"></a><br/></td></tr>
<tr class="separator:ga2e36fd210e4a1a5dd333ce80dd6d9a88"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>This set of functions implements Finite Impulse Response (FIR) lattice filters for Q15, Q31 and floating-point data types. Lattice filters are used in a variety of adaptive filter applications. The filter structure is feedforward and the net impulse response is finite length. The functions operate on blocks of input and output data and each call to the function processes <code>blockSize</code> samples through the filter. <code>pSrc</code> and <code>pDst</code> point to input and output arrays containing <code>blockSize</code> values.</p>
<dl class="section user"><dt>Algorithm: </dt><dd><div class="image">
<img src="FIRLattice.gif" alt="FIRLattice.gif"/>
<div class="caption">
Finite Impulse Response Lattice filter</div></div>
 The following difference equation is implemented: <pre>    
     f0[n] = g0[n] = x[n]    
     fm[n] = fm-1[n] + km * gm-1[n-1] for m = 1, 2, ...M    
     gm[n] = km * fm-1[n] + gm-1[n-1] for m = 1, 2, ...M    
     y[n] = fM[n]    
  </pre> </dd></dl>
<dl class="section user"><dt></dt><dd><code>pCoeffs</code> points to tha array of reflection coefficients of size <code>numStages</code>. Reflection Coefficients are stored in the following order. </dd></dl>
<dl class="section user"><dt></dt><dd><pre>    
     {k1, k2, ..., kM}    
  </pre> where M is number of stages </dd></dl>
<dl class="section user"><dt></dt><dd><code>pState</code> points to a state array of size <code>numStages</code>. The state variables (g values) hold previous inputs and are stored in the following order. <pre>    
     {g0[n], g1[n], g2[n] ...gM-1[n]}    
  </pre> The state variables are updated after each block of data is processed; the coefficients are untouched. </dd></dl>
<dl class="section user"><dt>Instance Structure </dt><dd>The coefficients and state variables for a filter are stored together in an instance data structure. A separate instance structure must be defined for each filter. Coefficient arrays may be shared among several instances while state variable arrays cannot be shared. There are separate instance structure declarations for each of the 3 supported data types.</dd></dl>
<dl class="section user"><dt>Initialization Functions </dt><dd>There is also an associated initialization function for each data type. The initialization function performs the following operations:<ul>
<li>Sets the values of the internal structure fields.</li>
<li>Zeros out the values in the state buffer. To do this manually without calling the init function, assign the follow subfields of the instance structure: numStages, pCoeffs, pState. Also set all of the values in pState to zero.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Use of the initialization function is optional. However, if the initialization function is used, then the instance structure cannot be placed into a const data section. To place an instance structure into a const data section, the instance structure must be manually initialized. Set the values in the state buffer to zeros and then manually initialize the instance structure as follows: <pre>    
*arm_fir_lattice_instance_f32 S = {numStages, pState, pCoeffs};    
*arm_fir_lattice_instance_q31 S = {numStages, pState, pCoeffs};    
*arm_fir_lattice_instance_q15 S = {numStages, pState, pCoeffs};    
  </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where <code>numStages</code> is the number of stages in the filter; <code>pState</code> is the address of the state buffer; <code>pCoeffs</code> is the address of the coefficient buffer. </dd></dl>
<dl class="section user"><dt>Fixed-Point Behavior </dt><dd>Care must be taken when using the fixed-point versions of the FIR Lattice filter functions. In particular, the overflow and saturation behavior of the accumulator used in each function must be considered. Refer to the function specific documentation below for usage guidelines. </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gae63a45a63a11a65f2eae8b8b1fe370a8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_lattice_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__fir__lattice__instance__f32.html">arm_fir_lattice_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point FIR lattice structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__fir__lattice__instance__f32.html#ad369bd9997a250f195254df37408a38f">arm_fir_lattice_instance_f32::numStages</a>, <a class="el" href="structarm__fir__lattice__instance__f32.html#a33bf5948c947f9ef80a99717cb0a0a43">arm_fir_lattice_instance_f32::pCoeffs</a>, and <a class="el" href="structarm__fir__lattice__instance__f32.html#ae348884a1ba9b83fadccd5da640cbcaf">arm_fir_lattice_instance_f32::pState</a>.</p>

</div>
</div>
<a class="anchor" id="ga86199a1590af2b8941c6532ee9d03229"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_lattice_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__fir__lattice__instance__f32.html">arm_fir_lattice_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numStages</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point FIR lattice structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numStages</td><td>number of filter stages. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the coefficient buffer. The array is of length numStages. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. The array is of length numStages. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="structarm__fir__lattice__instance__f32.html#ad369bd9997a250f195254df37408a38f">arm_fir_lattice_instance_f32::numStages</a>, <a class="el" href="structarm__fir__lattice__instance__f32.html#a33bf5948c947f9ef80a99717cb0a0a43">arm_fir_lattice_instance_f32::pCoeffs</a>, and <a class="el" href="structarm__fir__lattice__instance__f32.html#ae348884a1ba9b83fadccd5da640cbcaf">arm_fir_lattice_instance_f32::pState</a>.</p>

</div>
</div>
<a class="anchor" id="ga1b22f30ce1cc19bf5a5d7c9fca154d72"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_lattice_init_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__fir__lattice__instance__q15.html">arm_fir_lattice_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numStages</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 FIR lattice structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numStages</td><td>number of filter stages. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the coefficient buffer. The array is of length numStages. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. The array is of length numStages. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="structarm__fir__lattice__instance__q15.html#a38b179138d6a6c9cac4f8f79b6fd5357">arm_fir_lattice_instance_q15::numStages</a>, <a class="el" href="structarm__fir__lattice__instance__q15.html#a78f872826140069cf67836fff87360bc">arm_fir_lattice_instance_q15::pCoeffs</a>, and <a class="el" href="structarm__fir__lattice__instance__q15.html#a37b90dea2bc3ee7c9951a9fe74db0cbb">arm_fir_lattice_instance_q15::pState</a>.</p>

</div>
</div>
<a class="anchor" id="gac05a17a0188bb851b58d19e572870a54"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_lattice_init_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__fir__lattice__instance__q31.html">arm_fir_lattice_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numStages</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q31 FIR lattice structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numStages</td><td>number of filter stages. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pCoeffs</td><td>points to the coefficient buffer. The array is of length numStages. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. The array is of length numStages. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="structarm__fir__lattice__instance__q31.html#a9f3773bbb76bc5a8a5ee9d37786bf478">arm_fir_lattice_instance_q31::numStages</a>, <a class="el" href="structarm__fir__lattice__instance__q31.html#a66c3364bf5863cd45e05f1652c3dc522">arm_fir_lattice_instance_q31::pCoeffs</a>, and <a class="el" href="structarm__fir__lattice__instance__q31.html#a08fe9494ab7cd336b791e9657adadcf6">arm_fir_lattice_instance_q31::pState</a>.</p>

</div>
</div>
<a class="anchor" id="gabb0ab07fd313b4d863070c3ddca51542"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_lattice_q15 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__fir__lattice__instance__q15.html">arm_fir_lattice_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 FIR lattice structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__fir__lattice__instance__q15.html#a38b179138d6a6c9cac4f8f79b6fd5357">arm_fir_lattice_instance_q15::numStages</a>, <a class="el" href="structarm__fir__lattice__instance__q15.html#a78f872826140069cf67836fff87360bc">arm_fir_lattice_instance_q15::pCoeffs</a>, and <a class="el" href="structarm__fir__lattice__instance__q15.html#a37b90dea2bc3ee7c9951a9fe74db0cbb">arm_fir_lattice_instance_q15::pState</a>.</p>

</div>
</div>
<a class="anchor" id="ga2e36fd210e4a1a5dd333ce80dd6d9a88"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_fir_lattice_q31 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__fir__lattice__instance__q31.html">arm_fir_lattice_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q31 FIR lattice structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> In order to avoid overflows the input signal must be scaled down by 2*log2(numStages) bits. </p>

<p>References <a class="el" href="structarm__fir__lattice__instance__q31.html#a9f3773bbb76bc5a8a5ee9d37786bf478">arm_fir_lattice_instance_q31::numStages</a>, <a class="el" href="structarm__fir__lattice__instance__q31.html#a66c3364bf5863cd45e05f1652c3dc522">arm_fir_lattice_instance_q31::pCoeffs</a>, and <a class="el" href="structarm__fir__lattice__instance__q31.html#a08fe9494ab7cd336b791e9657adadcf6">arm_fir_lattice_instance_q31::pState</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
