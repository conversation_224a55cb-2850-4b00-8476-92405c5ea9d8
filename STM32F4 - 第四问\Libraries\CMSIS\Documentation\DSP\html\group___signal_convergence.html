<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Signal Convergence Example</title>
<title>CMSIS-DSP: Signal Convergence Example</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___signal_convergence.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Signal Convergence Example</div>  </div>
<div class="ingroups"><a class="el" href="group__group_examples.html">Examples</a></div></div><!--header-->
<div class="contents">
<dl class="section user"><dt>Description:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>Demonstrates the ability of an adaptive filter to "learn" the transfer function of a FIR lowpass filter using the Normalized LMS Filter, Finite Impulse Response (FIR) Filter, and Basic Math Functions.</dd></dl>
<dl class="section user"><dt>Algorithm:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The figure below illustrates the signal flow in this example. Uniformly distributed white noise is passed through an FIR lowpass filter. The output of the FIR filter serves as the reference input of the adaptive filter (normalized LMS filter). The white noise is input to the adaptive filter. The adaptive filter learns the transfer function of the FIR filter. The filter outputs two signals: (1) the output of the internal adaptive FIR filter, and (2) the error signal which is the difference between the adaptive filter and the reference output of the FIR filter. Over time as the adaptive filter learns the transfer function of the FIR filter, the first output approaches the reference output of the FIR filter, and the error signal approaches zero. </dd></dl>
<dl class="section user"><dt></dt><dd>The adaptive filter converges properly even if the input signal has a large dynamic range (i.e., varies from small to large values). The coefficients of the adaptive filter are initially zero, and then converge over 1536 samples. The internal function <a class="el" href="arm__signal__converge__example__f32_8c.html#a8f521e839d4fad24a4f12a18dfeae5d4">test_signal_converge()</a> implements the stopping condition. The function checks if all of the values of the error signal have a magnitude below a threshold DELTA.</dd></dl>
<dl class="section user"><dt>Block Diagram:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="SignalFlow.gif" alt="SignalFlow.gif"/>
</div>
</dd></dl>
<dl class="section user"><dt>Variables Description:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd><ul>
<li><code>testInput_f32</code> points to the input data </li>
<li><code>firStateF32</code> points to FIR state buffer </li>
<li><code>lmsStateF32</code> points to Normalised Least mean square FIR filter state buffer </li>
<li><code>FIRCoeff_f32</code> points to coefficient buffer </li>
<li><code>lmsNormCoeff_f32</code> points to Normalised Least mean square FIR filter coefficient buffer </li>
<li><code>wire1</code>, wir2, wire3 temporary buffers </li>
<li><code>errOutput</code>, err_signal temporary error buffers</li>
</ul>
</dd></dl>
<dl class="section user"><dt>CMSIS DSP Software Library Functions Used:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd><ul>
<li><a class="el" href="group___l_m_s___n_o_r_m.html#gac7ccbaea863882056eee815456464670" title="Initialization function for floating-point normalized LMS filter.">arm_lms_norm_init_f32()</a></li>
<li><a class="el" href="group___f_i_r.html#ga98d13def6427e29522829f945d0967db" title="Initialization function for the floating-point FIR filter.">arm_fir_init_f32()</a></li>
<li><a class="el" href="group___f_i_r.html#gae8fb334ea67eb6ecbd31824ddc14cd6a" title="Processing function for the floating-point FIR filter.">arm_fir_f32()</a></li>
<li><a class="el" href="group___l_m_s___n_o_r_m.html#ga2418c929087c6eba719758eaae3f3300" title="Processing function for floating-point normalized LMS filter.">arm_lms_norm_f32()</a></li>
<li><a class="el" href="group__scale.html#ga3487af88b112f682ee90589cd419e123" title="Multiplies a floating-point vector by a scalar.">arm_scale_f32()</a></li>
<li><a class="el" href="group___basic_abs.html#ga421b6275f9d35f50286c0ff3beceff02" title="Floating-point vector absolute value.">arm_abs_f32()</a></li>
<li><a class="el" href="group___basic_sub.html#ga7f975a472de286331134227c08aad826" title="Floating-point vector subtraction.">arm_sub_f32()</a></li>
<li><a class="el" href="group___min.html#gaf62b1673740fc516ea64daf777b7d74a" title="Minimum value of a floating-point vector.">arm_min_f32()</a></li>
<li><a class="el" href="group__copy.html#gadd1f737e677e0e6ca31767c7001417b3" title="Copies the elements of a floating-point vector.">arm_copy_f32()</a></li>
</ul>
</dd></dl>
<p><b> Refer </b> <a class="el" href="arm_signal_converge_example_f32_8c-example.html">arm_signal_converge_example_f32.c</a> </p>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:50 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
