/**
 ******************************************************************************
 * @file    ad9851_highperf.h
 * <AUTHOR> - AD9851方案
 * @version V1.0
 * @date    2025-01-01
 * @brief   AD9851高性能DDS信号发生器驱动 - 替换AD9834方案
 *          基于商家提供的AD9851模块驱动优化，实现1Hz-70MHz高精度输出
 ******************************************************************************
 * @attention
 *
 * 硬件连接 (嘉立创"天空星"STM32F407VGT6开发板 + AD9851模块):
 * PA3  --> AD9851_FQ_UP   (频率更新信号)
 * PA4  --> AD9851_W_CLK   (写时钟信号)
 * PA6  --> AD9851_RESET   (复位信号)
 * PC0-PC7 --> AD9851_D0-D7 (8位并行数据线)
 *
 * AD9851技术规格:
 * - 系统时钟: 180MHz (6倍频模式)
 * - 最大输出频率: 70MHz (实际使用1Hz-70MHz)
 * - 频率分辨率: 0.042Hz (32位频率字)
 * - 相位分辨率: 11.25° (5位相位字)
 * - SFDR: >70dB @10MHz
 * - 输出幅度: 可调节 (通过外部电路实现0.8Vpp)
 ******************************************************************************
 */

#ifndef __AD9851_HIGHPERF_H
#define __AD9851_HIGHPERF_H

#include "stm32f4xx.h"
#include "../Core/systick.h"

/* ==================== AD9851技术规格 (高精度优化) ==================== */
#define AD9851_SYSTEM_CLOCK         180000000ULL // 180MHz系统时钟 (6倍频模式)
#define AD9851_MAX_FREQ_HZ          70000000UL   // 70MHz最大使用频率
#define AD9851_MIN_FREQ_HZ          1UL          // 1Hz最小频率
#define AD9851_FREQ_RESOLUTION_HZ   0.042f       // 频率分辨率 (180MHz/2^32)
#define AD9851_FREQ_WORD_MAX        4294967296ULL // 2^32 (32位频率字最大值)
#define AD9851_FREQ_PRECISION       1000000ULL   // 计算精度提升因子

/* ==================== 幅度控制和校准参数 ==================== */
#define AD9851_DEFAULT_OUTPUT_VPP   0.940f       // AD9851默认输出峰峰值 (V)
#define AD9851_TARGET_OUTPUT_VPP    0.800f       // 目标输出峰峰值 (V)
#define AD9851_AMPLITUDE_SCALE      0.851f       // 幅度缩放因子 (0.8/0.94)
#define AD9851_AMPLITUDE_TOLERANCE  0.005f       // 幅度容差 (±5mV)
#define AD9851_CALIBRATION_SAMPLES  10           // 校准采样次数

/* ==================== 硬件引脚定义 (基于商家接线说明) ==================== */
// 控制信号引脚 (GPIOA) - 与商家驱动保持一致
#define AD9851_Control_Port         GPIOA
#define AD9851_FQ_UP                GPIO_Pin_3  // PA3 - 频率更新信号
#define AD9851_W_CLK                GPIO_Pin_4  // PA4 - 写时钟信号
#define AD9851_RESET                GPIO_Pin_6  // PA6 - 复位信号

// 8位并行数据线 (GPIOC) - PC0-PC7
#define AD9851_Data_Port            GPIOC
#define AD9851_DATA_MASK            0x00FF      // PC0-PC7数据掩码

/* ==================== 快速GPIO操作宏 (兼容商家驱动) ==================== */
#define AD9851_FQ_UP_SET            GPIO_SetBits(AD9851_Control_Port, AD9851_FQ_UP)
#define AD9851_FQ_UP_CLR            GPIO_ResetBits(AD9851_Control_Port, AD9851_FQ_UP)
#define AD9851_W_CLK_SET            GPIO_SetBits(AD9851_Control_Port, AD9851_W_CLK)
#define AD9851_W_CLK_CLR            GPIO_ResetBits(AD9851_Control_Port, AD9851_W_CLK)
#define AD9851_RESET_SET            GPIO_SetBits(AD9851_Control_Port, AD9851_RESET)
#define AD9851_RESET_CLR            GPIO_ResetBits(AD9851_Control_Port, AD9851_RESET)

// 快速数据总线操作 (基于商家驱动优化)
#define AD9851_SET_DATA(data)       do { \
    uint16_t temp = GPIOC->ODR & 0xFF00; \
    GPIOC->ODR = temp | ((data) & 0xFF); \
} while(0)

/* ==================== AD9851工作模式定义 ==================== */
#define AD9851_MODE_PARALLEL        0           // 并行模式 (默认)
#define AD9851_MODE_SERIAL          1           // 串行模式
#define AD9851_MULTIPLIER_1X        0           // 1倍频 (30MHz时钟)
#define AD9851_MULTIPLIER_6X        1           // 6倍频 (180MHz时钟，默认)

/* ==================== 频率和相位控制字定义 ==================== */
#define AD9851_PHASE_MASK           0x1F        // 5位相位控制字掩码
#define AD9851_POWERDOWN_BIT        0x04        // 功耗控制位
#define AD9851_MULTIPLIER_BIT       0x01        // 倍频控制位

/* ==================== 函数声明 (基于商家驱动优化) ==================== */

/**
 * @brief  AD9851初始化 (替换AD9834方案)
 * @param  None
 * @retval None
 */
void AD9851_Init(void);

/**
 * @brief  AD9851复位
 * @param  None
 * @retval None
 */
void AD9851_Reset(void);

/**
 * @brief  AD9851并行写入数据 (高速优化)
 * @param  w0: 控制字节
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void AD9851_WriteParallel(uint8_t w0, double frequency_hz);

/**
 * @brief  设置AD9851频率 (高精度1Hz-70MHz)
 * @param  frequency_hz: 频率值 (1Hz - 70MHz)
 * @retval None
 */
void AD9851_SetFrequency(double frequency_hz);

/**
 * @brief  设置AD9851相位
 * @param  phase_deg: 相位值 (0-360度)
 * @retval None
 */
void AD9851_SetPhase(float phase_deg);

/**
 * @brief  设置AD9851输出幅度 (通过控制字)
 * @param  amplitude_percent: 幅度百分比 (0-100%)
 * @retval None
 */
void AD9851_SetAmplitude(uint8_t amplitude_percent);

/**
 * @brief  AD9851功耗控制
 * @param  enable: true-正常工作, false-低功耗模式
 * @retval None
 */
void AD9851_PowerControl(bool enable);

/**
 * @brief  频率稳定性增强 (针对5MHz优化)
 * @param  frequency_hz: 要稳定的频率
 * @retval None
 */
void AD9851_StabilizeFrequency(uint32_t frequency_hz);

/**
 * @brief  高精度频率设置 (电赛专用，误差<0.001%)
 * @param  frequency_hz: 精确频率值 (Hz)
 * @retval 实际设置的频率值
 */
uint32_t AD9851_SetFrequency_Precision(uint32_t frequency_hz);

/**
 * @brief  设置5MHz正弦波输出 (项目专用)
 * @param  amplitude_vpp: 峰峰值电压 (V)
 * @retval None
 */
void AD9851_Set5MHz_Sine(float amplitude_vpp);

/**
 * @brief  幅度校准和稳定控制 (新增)
 * @param  target_vpp: 目标峰峰值 (V)
 * @retval 实际校准后的峰峰值 (V)
 */
float AD9851_CalibrateAmplitude(float target_vpp);

/**
 * @brief  精确设置0.8V峰峰值输出 (专用函数)
 * @param  None
 * @retval 实际输出峰峰值 (V)
 */
float AD9851_Set800mV_Precise(void);

/**
 * @brief  幅度稳定性监控和自动调整
 * @param  target_vpp: 目标峰峰值 (V)
 * @retval 当前实际峰峰值 (V)
 */
float AD9851_AmplitudeStabilize(float target_vpp);

/**
 * @brief  获取当前输出幅度估算值
 * @param  None
 * @retval 估算的峰峰值 (V)
 */
float AD9851_GetCurrentAmplitude(void);

#endif /* __AD9851_HIGHPERF_H */
