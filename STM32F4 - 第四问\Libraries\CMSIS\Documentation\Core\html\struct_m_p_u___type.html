<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>MPU_Type Struct Reference</title>
<title>CMSIS-CORE: MPU_Type Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('struct_m_p_u___type.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">MPU_Type Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Structure type to access the Memory Protection Unit (MPU).  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a6ae8a8c3a4909ae41447168d793608f7"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_p_u___type.html#a6ae8a8c3a4909ae41447168d793608f7">TYPE</a></td></tr>
<tr class="memdesc:a6ae8a8c3a4909ae41447168d793608f7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x000 (R/ ) MPU Type Register.  <a href="#a6ae8a8c3a4909ae41447168d793608f7"></a><br/></td></tr>
<tr class="separator:a6ae8a8c3a4909ae41447168d793608f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab33593671948b93b1c0908d78779328"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_p_u___type.html#aab33593671948b93b1c0908d78779328">CTRL</a></td></tr>
<tr class="memdesc:aab33593671948b93b1c0908d78779328"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x004 (R/W) MPU Control Register.  <a href="#aab33593671948b93b1c0908d78779328"></a><br/></td></tr>
<tr class="separator:aab33593671948b93b1c0908d78779328"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd8de96a5d574c3953e2106e782f9833"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_p_u___type.html#afd8de96a5d574c3953e2106e782f9833">RNR</a></td></tr>
<tr class="memdesc:afd8de96a5d574c3953e2106e782f9833"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x008 (R/W) MPU Region RNRber Register.  <a href="#afd8de96a5d574c3953e2106e782f9833"></a><br/></td></tr>
<tr class="separator:afd8de96a5d574c3953e2106e782f9833"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f2e2448a77aadacd9f394f6c4c708d9"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_p_u___type.html#a3f2e2448a77aadacd9f394f6c4c708d9">RBAR</a></td></tr>
<tr class="memdesc:a3f2e2448a77aadacd9f394f6c4c708d9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x00C (R/W) MPU Region Base Address Register.  <a href="#a3f2e2448a77aadacd9f394f6c4c708d9"></a><br/></td></tr>
<tr class="separator:a3f2e2448a77aadacd9f394f6c4c708d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc65d266d15ce9ba57b3d127e8267f03"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_p_u___type.html#adc65d266d15ce9ba57b3d127e8267f03">RASR</a></td></tr>
<tr class="memdesc:adc65d266d15ce9ba57b3d127e8267f03"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x010 (R/W) MPU Region Attribute and Size Register.  <a href="#adc65d266d15ce9ba57b3d127e8267f03"></a><br/></td></tr>
<tr class="separator:adc65d266d15ce9ba57b3d127e8267f03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4dbcffa0a71c31e521b645b34b40e639"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_p_u___type.html#a4dbcffa0a71c31e521b645b34b40e639">RBAR_A1</a></td></tr>
<tr class="memdesc:a4dbcffa0a71c31e521b645b34b40e639"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x014 (R/W) MPU Alias 1 Region Base Address Register.  <a href="#a4dbcffa0a71c31e521b645b34b40e639"></a><br/></td></tr>
<tr class="separator:a4dbcffa0a71c31e521b645b34b40e639"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a94222f9a8637b5329016e18f08af7185"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_p_u___type.html#a94222f9a8637b5329016e18f08af7185">RASR_A1</a></td></tr>
<tr class="memdesc:a94222f9a8637b5329016e18f08af7185"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x018 (R/W) MPU Alias 1 Region Attribute and Size Register.  <a href="#a94222f9a8637b5329016e18f08af7185"></a><br/></td></tr>
<tr class="separator:a94222f9a8637b5329016e18f08af7185"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8703a00626dba046b841c0db6c78c395"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_p_u___type.html#a8703a00626dba046b841c0db6c78c395">RBAR_A2</a></td></tr>
<tr class="memdesc:a8703a00626dba046b841c0db6c78c395"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x01C (R/W) MPU Alias 2 Region Base Address Register.  <a href="#a8703a00626dba046b841c0db6c78c395"></a><br/></td></tr>
<tr class="separator:a8703a00626dba046b841c0db6c78c395"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aac7727a6225c6aa00627c36d51d014"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_p_u___type.html#a0aac7727a6225c6aa00627c36d51d014">RASR_A2</a></td></tr>
<tr class="memdesc:a0aac7727a6225c6aa00627c36d51d014"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x020 (R/W) MPU Alias 2 Region Attribute and Size Register.  <a href="#a0aac7727a6225c6aa00627c36d51d014"></a><br/></td></tr>
<tr class="separator:a0aac7727a6225c6aa00627c36d51d014"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9fda17c37b85ef317c7c8688ff8c5804"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_p_u___type.html#a9fda17c37b85ef317c7c8688ff8c5804">RBAR_A3</a></td></tr>
<tr class="memdesc:a9fda17c37b85ef317c7c8688ff8c5804"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x024 (R/W) MPU Alias 3 Region Base Address Register.  <a href="#a9fda17c37b85ef317c7c8688ff8c5804"></a><br/></td></tr>
<tr class="separator:a9fda17c37b85ef317c7c8688ff8c5804"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aced0b908173b9a4bae4f59452f0cdb0d"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_m_p_u___type.html#aced0b908173b9a4bae4f59452f0cdb0d">RASR_A3</a></td></tr>
<tr class="memdesc:aced0b908173b9a4bae4f59452f0cdb0d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x028 (R/W) MPU Alias 3 Region Attribute and Size Register.  <a href="#aced0b908173b9a4bae4f59452f0cdb0d"></a><br/></td></tr>
<tr class="separator:aced0b908173b9a4bae4f59452f0cdb0d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="aab33593671948b93b1c0908d78779328"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t MPU_Type::CTRL</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="adc65d266d15ce9ba57b3d127e8267f03"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t MPU_Type::RASR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a94222f9a8637b5329016e18f08af7185"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t MPU_Type::RASR_A1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0aac7727a6225c6aa00627c36d51d014"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t MPU_Type::RASR_A2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aced0b908173b9a4bae4f59452f0cdb0d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t MPU_Type::RASR_A3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3f2e2448a77aadacd9f394f6c4c708d9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t MPU_Type::RBAR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4dbcffa0a71c31e521b645b34b40e639"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t MPU_Type::RBAR_A1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8703a00626dba046b841c0db6c78c395"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t MPU_Type::RBAR_A2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9fda17c37b85ef317c7c8688ff8c5804"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t MPU_Type::RBAR_A3</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="afd8de96a5d574c3953e2106e782f9833"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t MPU_Type::RNR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6ae8a8c3a4909ae41447168d793608f7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t MPU_Type::TYPE</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_m_p_u___type.html">MPU_Type</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
