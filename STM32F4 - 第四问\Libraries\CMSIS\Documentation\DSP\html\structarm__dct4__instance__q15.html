<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_dct4_instance_q15 Struct Reference</title>
<title>CMSIS-DSP: arm_dct4_instance_q15 Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('structarm__dct4__instance__q15.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">arm_dct4_instance_q15 Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Instance structure for the Q15 DCT4/IDCT4 function.  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a53d24009bb9b2e93d0aa07db7f1a6c25"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__dct4__instance__q15.html#a53d24009bb9b2e93d0aa07db7f1a6c25">N</a></td></tr>
<tr class="separator:a53d24009bb9b2e93d0aa07db7f1a6c25"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af43dcbbc2fc661ffbc525afe3dcbd7da"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__dct4__instance__q15.html#af43dcbbc2fc661ffbc525afe3dcbd7da">Nby2</a></td></tr>
<tr class="separator:af43dcbbc2fc661ffbc525afe3dcbd7da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a197098140d68e89a08f7a249003a0b86"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__dct4__instance__q15.html#a197098140d68e89a08f7a249003a0b86">normalize</a></td></tr>
<tr class="separator:a197098140d68e89a08f7a249003a0b86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc6c847e9f906781e1d5da40e9aafa76"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__dct4__instance__q15.html#abc6c847e9f906781e1d5da40e9aafa76">pTwiddle</a></td></tr>
<tr class="separator:abc6c847e9f906781e1d5da40e9aafa76"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac76df681b1bd502fb4874c06f055dded"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__dct4__instance__q15.html#ac76df681b1bd502fb4874c06f055dded">pCosFactor</a></td></tr>
<tr class="separator:ac76df681b1bd502fb4874c06f055dded"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11cf95c1cd9dd2dd5e4b81b8f88dc208"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structarm__rfft__instance__q15.html">arm_rfft_instance_q15</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__dct4__instance__q15.html#a11cf95c1cd9dd2dd5e4b81b8f88dc208">pRfft</a></td></tr>
<tr class="separator:a11cf95c1cd9dd2dd5e4b81b8f88dc208"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7284932ee8c36107c33815eb62eadffc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structarm__cfft__radix4__instance__q15.html">arm_cfft_radix4_instance_q15</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structarm__dct4__instance__q15.html#a7284932ee8c36107c33815eb62eadffc">pCfft</a></td></tr>
<tr class="separator:a7284932ee8c36107c33815eb62eadffc"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="a53d24009bb9b2e93d0aa07db7f1a6c25"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t arm_dct4_instance_q15::N</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>length of the DCT4. </p>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>, and <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga114cb9635059f678df291fcc887aaf2b">arm_dct4_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="af43dcbbc2fc661ffbc525afe3dcbd7da"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t arm_dct4_instance_q15::Nby2</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>half of the length of the DCT4. </p>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>, and <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga114cb9635059f678df291fcc887aaf2b">arm_dct4_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="a197098140d68e89a08f7a249003a0b86"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> arm_dct4_instance_q15::normalize</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>normalizing factor. </p>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="a7284932ee8c36107c33815eb62eadffc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structarm__cfft__radix4__instance__q15.html">arm_cfft_radix4_instance_q15</a>* arm_dct4_instance_q15::pCfft</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the complex FFT instance. </p>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="ac76df681b1bd502fb4874c06f055dded"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>* arm_dct4_instance_q15::pCosFactor</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the cosFactor table. </p>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>, and <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga114cb9635059f678df291fcc887aaf2b">arm_dct4_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="a11cf95c1cd9dd2dd5e4b81b8f88dc208"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structarm__rfft__instance__q15.html">arm_rfft_instance_q15</a>* arm_dct4_instance_q15::pRfft</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the real FFT instance. </p>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="abc6c847e9f906781e1d5da40e9aafa76"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a>* arm_dct4_instance_q15::pTwiddle</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>points to the twiddle factor table. </p>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>, and <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga114cb9635059f678df291fcc887aaf2b">arm_dct4_q15()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structarm__dct4__instance__q15.html">arm_dct4_instance_q15</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
