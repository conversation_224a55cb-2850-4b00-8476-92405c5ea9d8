<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Data Fields</title>
<title>CMSIS-CORE: Data Fields</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="#index__"><span>_</span></a></li>
      <li><a href="#index_a"><span>a</span></a></li>
      <li><a href="#index_b"><span>b</span></a></li>
      <li><a href="#index_c"><span>c</span></a></li>
      <li><a href="#index_d"><span>d</span></a></li>
      <li><a href="#index_e"><span>e</span></a></li>
      <li><a href="#index_f"><span>f</span></a></li>
      <li><a href="#index_h"><span>h</span></a></li>
      <li><a href="#index_i"><span>i</span></a></li>
      <li><a href="#index_l"><span>l</span></a></li>
      <li><a href="#index_m"><span>m</span></a></li>
      <li><a href="#index_n"><span>n</span></a></li>
      <li><a href="#index_p"><span>p</span></a></li>
      <li><a href="#index_q"><span>q</span></a></li>
      <li><a href="#index_r"><span>r</span></a></li>
      <li><a href="#index_s"><span>s</span></a></li>
      <li><a href="#index_t"><span>t</span></a></li>
      <li><a href="#index_u"><span>u</span></a></li>
      <li><a href="#index_v"><span>v</span></a></li>
      <li><a href="#index_w"><span>w</span></a></li>
      <li><a href="#index_z"><span>z</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('functions.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all struct and union fields with links to the structures/unions they belong to:</div>

<h3><a class="anchor" id="index__"></a>- _ -</h3><ul>
<li>_reserved0
: <a class="el" href="union_a_p_s_r___type.html#afbce95646fd514c10aa85ec0a33db728">APSR_Type</a>
, <a class="el" href="union_c_o_n_t_r_o_l___type.html#af8c314273a1e4970a5671bd7f8184f50">CONTROL_Type</a>
, <a class="el" href="unionx_p_s_r___type.html#af438e0f407357e914a70b5bd4d6a97c5">xPSR_Type</a>
, <a class="el" href="union_i_p_s_r___type.html#ad2eb0a06de4f03f58874a727716aa9aa">IPSR_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_a"></a>- a -</h3><ul>
<li>ACPR
: <a class="el" href="struct_t_p_i___type.html#ad75832a669eb121f6fce3c28d36b7fab">TPI_Type</a>
</li>
<li>ACTLR
: <a class="el" href="struct_s_cn_s_c_b___type.html#aacadedade30422fed705e8dfc8e6cd8d">SCnSCB_Type</a>
</li>
<li>ADR
: <a class="el" href="struct_s_c_b___type.html#aaedf846e435ed05c68784b40d3db2bf2">SCB_Type</a>
</li>
<li>AFSR
: <a class="el" href="struct_s_c_b___type.html#aeb77053c84f49c261ab5b8374e8958ef">SCB_Type</a>
</li>
<li>AIRCR
: <a class="el" href="struct_s_c_b___type.html#a6ed3c9064013343ea9fd0a73a734f29d">SCB_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_b"></a>- b -</h3><ul>
<li>b
: <a class="el" href="union_a_p_s_r___type.html#a7dbc79a057ded4b11ca5323fc2d5ab14">APSR_Type</a>
, <a class="el" href="union_i_p_s_r___type.html#add0d6497bd50c25569ea22b48a03ec50">IPSR_Type</a>
, <a class="el" href="union_c_o_n_t_r_o_l___type.html#adc6a38ab2980d0e9577b5a871da14eb9">CONTROL_Type</a>
, <a class="el" href="unionx_p_s_r___type.html#a3b1063bb5cdad67e037cba993b693b70">xPSR_Type</a>
</li>
<li>BFAR
: <a class="el" href="struct_s_c_b___type.html#a31f79afe86c949c9862e7d5fce077c3a">SCB_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_c"></a>- c -</h3><ul>
<li>C
: <a class="el" href="union_a_p_s_r___type.html#a86e2c5b891ecef1ab55b1edac0da79a6">APSR_Type</a>
, <a class="el" href="unionx_p_s_r___type.html#a40213a6b5620410cac83b0d89564609d">xPSR_Type</a>
</li>
<li>CALIB
: <a class="el" href="struct_sys_tick___type.html#a9c9eda0ea6f6a7c904d2d75a6963e238">SysTick_Type</a>
</li>
<li>CCR
: <a class="el" href="struct_s_c_b___type.html#a6d273c6b90bad15c91dfbbad0f6e92d8">SCB_Type</a>
</li>
<li>CFSR
: <a class="el" href="struct_s_c_b___type.html#a2f94bf549b16fdeb172352e22309e3c4">SCB_Type</a>
</li>
<li>CLAIMCLR
: <a class="el" href="struct_t_p_i___type.html#a44efa6045512c8d4da64b0623f7a43ad">TPI_Type</a>
</li>
<li>CLAIMSET
: <a class="el" href="struct_t_p_i___type.html#a2e4d5a07fabd771fa942a171230a0a84">TPI_Type</a>
</li>
<li>COMP0
: <a class="el" href="struct_d_w_t___type.html#a7cf71ff4b30a8362690fddd520763904">DWT_Type</a>
</li>
<li>COMP1
: <a class="el" href="struct_d_w_t___type.html#a4a5bb70a5ce3752bd628d5ce5658cb0c">DWT_Type</a>
</li>
<li>COMP2
: <a class="el" href="struct_d_w_t___type.html#a8927aedbe9fd6bdae8983088efc83332">DWT_Type</a>
</li>
<li>COMP3
: <a class="el" href="struct_d_w_t___type.html#a3df15697eec279dbbb4b4e9d9ae8b62f">DWT_Type</a>
</li>
<li>CPACR
: <a class="el" href="struct_s_c_b___type.html#af460b56ce524a8e3534173f0aee78e85">SCB_Type</a>
</li>
<li>CPICNT
: <a class="el" href="struct_d_w_t___type.html#a88cca2ab8eb1b5b507817656ceed89fc">DWT_Type</a>
</li>
<li>CPUID
: <a class="el" href="struct_s_c_b___type.html#afa7a9ee34dfa1da0b60b4525da285032">SCB_Type</a>
</li>
<li>CSPSR
: <a class="el" href="struct_t_p_i___type.html#aa723ef3d38237aa2465779b3cc73a94a">TPI_Type</a>
</li>
<li>CTRL
: <a class="el" href="struct_d_w_t___type.html#a37964d64a58551b69ce4c8097210d37d">DWT_Type</a>
, <a class="el" href="struct_m_p_u___type.html#aab33593671948b93b1c0908d78779328">MPU_Type</a>
, <a class="el" href="struct_sys_tick___type.html#af2ad94ac83e5d40fc6e34884bc1bec5f">SysTick_Type</a>
</li>
<li>CYCCNT
: <a class="el" href="struct_d_w_t___type.html#a71680298e85e96e57002f87e7ab78fd4">DWT_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_d"></a>- d -</h3><ul>
<li>DCRDR
: <a class="el" href="struct_core_debug___type.html#ab8f4bb076402b61f7be6308075a789c9">CoreDebug_Type</a>
</li>
<li>DCRSR
: <a class="el" href="struct_core_debug___type.html#afefa84bce7497652353a1b76d405d983">CoreDebug_Type</a>
</li>
<li>DEMCR
: <a class="el" href="struct_core_debug___type.html#a5cdd51dbe3ebb7041880714430edd52d">CoreDebug_Type</a>
</li>
<li>DEVID
: <a class="el" href="struct_t_p_i___type.html#a4b2e0d680cf7e26728ca8966363a938d">TPI_Type</a>
</li>
<li>DEVTYPE
: <a class="el" href="struct_t_p_i___type.html#a16d12c5b1e12f764fa3ec4a51c5f0f35">TPI_Type</a>
</li>
<li>DFR
: <a class="el" href="struct_s_c_b___type.html#a586a5225467262b378c0f231ccc77f86">SCB_Type</a>
</li>
<li>DFSR
: <a class="el" href="struct_s_c_b___type.html#ad7d61d9525fa9162579c3da0b87bff8d">SCB_Type</a>
</li>
<li>DHCSR
: <a class="el" href="struct_core_debug___type.html#a25c14c022c73a725a1736e903431095d">CoreDebug_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_e"></a>- e -</h3><ul>
<li>EXCCNT
: <a class="el" href="struct_d_w_t___type.html#ac0801a2328f3431e4706fed91c828f82">DWT_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_f"></a>- f -</h3><ul>
<li>FFCR
: <a class="el" href="struct_t_p_i___type.html#a3eb42d69922e340037692424a69da880">TPI_Type</a>
</li>
<li>FFSR
: <a class="el" href="struct_t_p_i___type.html#ae67849b2c1016fe6ef9095827d16cddd">TPI_Type</a>
</li>
<li>FIFO0
: <a class="el" href="struct_t_p_i___type.html#ae91ff529e87d8e234343ed31bcdc4f10">TPI_Type</a>
</li>
<li>FIFO1
: <a class="el" href="struct_t_p_i___type.html#aebaa9b8dd27f8017dd4f92ecf32bac8e">TPI_Type</a>
</li>
<li>FOLDCNT
: <a class="el" href="struct_d_w_t___type.html#a35f2315f870a574e3e6958face6584ab">DWT_Type</a>
</li>
<li>FPCA
: <a class="el" href="union_c_o_n_t_r_o_l___type.html#ac62cfff08e6f055e0101785bad7094cd">CONTROL_Type</a>
</li>
<li>FPCAR
: <a class="el" href="struct_f_p_u___type.html#aa48253f088dc524de80c42fbc995f66b">FPU_Type</a>
</li>
<li>FPCCR
: <a class="el" href="struct_f_p_u___type.html#a22054423086a3daf2077fb2f3fe2a8b8">FPU_Type</a>
</li>
<li>FPDSCR
: <a class="el" href="struct_f_p_u___type.html#a4d58ef3ebea69a5ec5acd8c90a9941b6">FPU_Type</a>
</li>
<li>FSCR
: <a class="el" href="struct_t_p_i___type.html#a377b78fe804f327e6f8b3d0f37e7bfef">TPI_Type</a>
</li>
<li>FUNCTION0
: <a class="el" href="struct_d_w_t___type.html#a5fbd9947d110cc168941f6acadc4a729">DWT_Type</a>
</li>
<li>FUNCTION1
: <a class="el" href="struct_d_w_t___type.html#a3345a33476ee58e165447a3212e6d747">DWT_Type</a>
</li>
<li>FUNCTION2
: <a class="el" href="struct_d_w_t___type.html#acba1654190641a3617fcc558b5e3f87b">DWT_Type</a>
</li>
<li>FUNCTION3
: <a class="el" href="struct_d_w_t___type.html#a80bd242fc05ca80f9db681ce4d82e890">DWT_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_h"></a>- h -</h3><ul>
<li>HFSR
: <a class="el" href="struct_s_c_b___type.html#a7bed53391da4f66d8a2a236a839d4c3d">SCB_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_i"></a>- i -</h3><ul>
<li>IABR
: <a class="el" href="struct_n_v_i_c___type.html#a33e917b381e08dabe4aa5eb2881a7c11">NVIC_Type</a>
</li>
<li>ICER
: <a class="el" href="struct_n_v_i_c___type.html#a1965a2e68b61d2e2009621f6949211a5">NVIC_Type</a>
</li>
<li>ICPR
: <a class="el" href="struct_n_v_i_c___type.html#a46241be64208436d35c9a4f8552575c5">NVIC_Type</a>
</li>
<li>ICSR
: <a class="el" href="struct_s_c_b___type.html#a3e66570ab689d28aebefa7e84e85dc4a">SCB_Type</a>
</li>
<li>ICTR
: <a class="el" href="struct_s_cn_s_c_b___type.html#ad99a25f5d4c163d9005ca607c24f6a98">SCnSCB_Type</a>
</li>
<li>IP
: <a class="el" href="struct_n_v_i_c___type.html#a6524789fedb94623822c3e0a47f3d06c">NVIC_Type</a>
</li>
<li>ISAR
: <a class="el" href="struct_s_c_b___type.html#acee8e458f054aac964268f4fe647ea4f">SCB_Type</a>
</li>
<li>ISER
: <a class="el" href="struct_n_v_i_c___type.html#af90c80b7c2b48e248780b3781e0df80f">NVIC_Type</a>
</li>
<li>ISPR
: <a class="el" href="struct_n_v_i_c___type.html#acf8e38fc2e97316242ddeb7ea959ab90">NVIC_Type</a>
</li>
<li>ISR
: <a class="el" href="union_i_p_s_r___type.html#ab46e5f1b2f4d17cfb9aca4fffcbb2fa5">IPSR_Type</a>
, <a class="el" href="unionx_p_s_r___type.html#a3e9120dcf1a829fc8d2302b4d0673970">xPSR_Type</a>
</li>
<li>IT
: <a class="el" href="unionx_p_s_r___type.html#a3200966922a194d84425e2807a7f1328">xPSR_Type</a>
</li>
<li>ITATBCTR0
: <a class="el" href="struct_t_p_i___type.html#a20ca7fad4d4009c242f20a7b4a44b7d0">TPI_Type</a>
</li>
<li>ITATBCTR2
: <a class="el" href="struct_t_p_i___type.html#a176d991adb4c022bd5b982a9f8fa6a1d">TPI_Type</a>
</li>
<li>ITCTRL
: <a class="el" href="struct_t_p_i___type.html#ab49c2cb6b5fe082746a444e07548c198">TPI_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_l"></a>- l -</h3><ul>
<li>LOAD
: <a class="el" href="struct_sys_tick___type.html#ae7bc9d3eac1147f3bba8d73a8395644f">SysTick_Type</a>
</li>
<li>LSUCNT
: <a class="el" href="struct_d_w_t___type.html#aeba92e6c7fd3de4ba06bfd94f47f5b35">DWT_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_m"></a>- m -</h3><ul>
<li>MASK0
: <a class="el" href="struct_d_w_t___type.html#a5bb1c17fc754180cc197b874d3d8673f">DWT_Type</a>
</li>
<li>MASK1
: <a class="el" href="struct_d_w_t___type.html#a0c684438a24f8c927e6e01c0e0a605ef">DWT_Type</a>
</li>
<li>MASK2
: <a class="el" href="struct_d_w_t___type.html#a8ecdc8f0d917dac86b0373532a1c0e2e">DWT_Type</a>
</li>
<li>MASK3
: <a class="el" href="struct_d_w_t___type.html#ae3f01137a8d28c905ddefe7333547fba">DWT_Type</a>
</li>
<li>MMFAR
: <a class="el" href="struct_s_c_b___type.html#ac49b24b3f222508464f111772f2c44dd">SCB_Type</a>
</li>
<li>MMFR
: <a class="el" href="struct_s_c_b___type.html#aec2f8283d2737c6897188568a4214976">SCB_Type</a>
</li>
<li>MVFR0
: <a class="el" href="struct_f_p_u___type.html#a135577b0a76bd3164be2a02f29ca46f1">FPU_Type</a>
</li>
<li>MVFR1
: <a class="el" href="struct_f_p_u___type.html#a776e8625853e1413c4e8330ec85c256d">FPU_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_n"></a>- n -</h3><ul>
<li>N
: <a class="el" href="union_a_p_s_r___type.html#a7e7bbba9b00b0bb3283dc07f1abe37e0">APSR_Type</a>
, <a class="el" href="unionx_p_s_r___type.html#a2db9a52f6d42809627d1a7a607c5dbc5">xPSR_Type</a>
</li>
<li>nPRIV
: <a class="el" href="union_c_o_n_t_r_o_l___type.html#a35c1732cf153b7b5c4bd321cf1de9605">CONTROL_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_p"></a>- p -</h3><ul>
<li>PCSR
: <a class="el" href="struct_d_w_t___type.html#abc5ae11d98da0ad5531a5e979a3c2ab5">DWT_Type</a>
</li>
<li>PFR
: <a class="el" href="struct_s_c_b___type.html#a3f51c43f952f3799951d0c54e76b0cb7">SCB_Type</a>
</li>
<li>PORT
: <a class="el" href="struct_i_t_m___type.html#afe056e8c8f8c5519d9b47611fa3a4c46">ITM_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_q"></a>- q -</h3><ul>
<li>Q
: <a class="el" href="union_a_p_s_r___type.html#a22d10913489d24ab08bd83457daa88de">APSR_Type</a>
, <a class="el" href="unionx_p_s_r___type.html#add7cbd2b0abd8954d62cd7831796ac7c">xPSR_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_r"></a>- r -</h3><ul>
<li>RASR
: <a class="el" href="struct_m_p_u___type.html#adc65d266d15ce9ba57b3d127e8267f03">MPU_Type</a>
</li>
<li>RASR_A1
: <a class="el" href="struct_m_p_u___type.html#a94222f9a8637b5329016e18f08af7185">MPU_Type</a>
</li>
<li>RASR_A2
: <a class="el" href="struct_m_p_u___type.html#a0aac7727a6225c6aa00627c36d51d014">MPU_Type</a>
</li>
<li>RASR_A3
: <a class="el" href="struct_m_p_u___type.html#aced0b908173b9a4bae4f59452f0cdb0d">MPU_Type</a>
</li>
<li>RBAR
: <a class="el" href="struct_m_p_u___type.html#a3f2e2448a77aadacd9f394f6c4c708d9">MPU_Type</a>
</li>
<li>RBAR_A1
: <a class="el" href="struct_m_p_u___type.html#a4dbcffa0a71c31e521b645b34b40e639">MPU_Type</a>
</li>
<li>RBAR_A2
: <a class="el" href="struct_m_p_u___type.html#a8703a00626dba046b841c0db6c78c395">MPU_Type</a>
</li>
<li>RBAR_A3
: <a class="el" href="struct_m_p_u___type.html#a9fda17c37b85ef317c7c8688ff8c5804">MPU_Type</a>
</li>
<li>RESERVED0
: <a class="el" href="struct_n_v_i_c___type.html#a2de17698945ea49abd58a2d45bdc9c80">NVIC_Type</a>
, <a class="el" href="struct_s_c_b___type.html#ac89a5d9901e3748d22a7090bfca2bee6">SCB_Type</a>
, <a class="el" href="struct_s_cn_s_c_b___type.html#afe1d5fd2966d5062716613b05c8d0ae1">SCnSCB_Type</a>
, <a class="el" href="struct_i_t_m___type.html#a2c5ae30385b5f370d023468ea9914c0e">ITM_Type</a>
, <a class="el" href="struct_f_p_u___type.html#a7b2967b069046c8544adbbc1db143a36">FPU_Type</a>
, <a class="el" href="struct_d_w_t___type.html#addd893d655ed90d40705b20170daac59">DWT_Type</a>
, <a class="el" href="struct_t_p_i___type.html#af143c5e8fc9a3b2be2878e9c1f331aa9">TPI_Type</a>
</li>
<li>RESERVED1
: <a class="el" href="struct_i_t_m___type.html#afffce5b93bbfedbaee85357d0b07ebce">ITM_Type</a>
, <a class="el" href="struct_d_w_t___type.html#a069871233a8c1df03521e6d7094f1de4">DWT_Type</a>
, <a class="el" href="struct_t_p_i___type.html#ac3956fe93987b725d89d3be32738da12">TPI_Type</a>
</li>
<li>RESERVED2
: <a class="el" href="struct_i_t_m___type.html#af56b2f07bc6b42cd3e4d17e1b27cff7b">ITM_Type</a>
, <a class="el" href="struct_d_w_t___type.html#a8556ca1c32590517602d92fe0cd55738">DWT_Type</a>
, <a class="el" href="struct_t_p_i___type.html#ac7bbb92e6231b9b38ac483f7d161a096">TPI_Type</a>
, <a class="el" href="struct_n_v_i_c___type.html#a0953af43af8ec7fd5869a1d826ce5b72">NVIC_Type</a>
</li>
<li>RESERVED3
: <a class="el" href="struct_t_p_i___type.html#a31700c8cdd26e4c094db72af33d9f24c">TPI_Type</a>
, <a class="el" href="struct_n_v_i_c___type.html#a9dd330835dbf21471e7b5be8692d77ab">NVIC_Type</a>
</li>
<li>RESERVED4
: <a class="el" href="struct_n_v_i_c___type.html#a5c0e5d507ac3c1bd5cdaaf9bbd177790">NVIC_Type</a>
, <a class="el" href="struct_t_p_i___type.html#a684071216fafee4e80be6aaa932cec46">TPI_Type</a>
</li>
<li>RESERVED5
: <a class="el" href="struct_t_p_i___type.html#a3f80dd93f6bab6524603a7aa58de9a30">TPI_Type</a>
, <a class="el" href="struct_n_v_i_c___type.html#a4f753b4f824270175af045ac99bc12e8">NVIC_Type</a>
</li>
<li>RESERVED7
: <a class="el" href="struct_t_p_i___type.html#a476ca23fbc9480f1697fbec871130550">TPI_Type</a>
</li>
<li>RNR
: <a class="el" href="struct_m_p_u___type.html#afd8de96a5d574c3953e2106e782f9833">MPU_Type</a>
</li>
<li>RSERVED1
: <a class="el" href="struct_n_v_i_c___type.html#a6d1daf7ab6f2ba83f57ff67ae6f571fe">NVIC_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_s"></a>- s -</h3><ul>
<li>SCR
: <a class="el" href="struct_s_c_b___type.html#abfad14e7b4534d73d329819625d77a16">SCB_Type</a>
</li>
<li>SHCSR
: <a class="el" href="struct_s_c_b___type.html#ae9891a59abbe51b0b2067ca507ca212f">SCB_Type</a>
</li>
<li>SHP
: <a class="el" href="struct_s_c_b___type.html#af6336103f8be0cab29de51daed5a65f4">SCB_Type</a>
</li>
<li>SLEEPCNT
: <a class="el" href="struct_d_w_t___type.html#a8afd5a4bf994011748bc012fa442c74d">DWT_Type</a>
</li>
<li>SPPR
: <a class="el" href="struct_t_p_i___type.html#a3eb655f2e45d7af358775025c1a50c8e">TPI_Type</a>
</li>
<li>SPSEL
: <a class="el" href="union_c_o_n_t_r_o_l___type.html#a8cc085fea1c50a8bd9adea63931ee8e2">CONTROL_Type</a>
</li>
<li>SSPSR
: <a class="el" href="struct_t_p_i___type.html#a158e9d784f6ee6398f4bdcb2e4ca0912">TPI_Type</a>
</li>
<li>STIR
: <a class="el" href="struct_n_v_i_c___type.html#a0b0d7f3131da89c659a2580249432749">NVIC_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_t"></a>- t -</h3><ul>
<li>T
: <a class="el" href="unionx_p_s_r___type.html#a7eed9fe24ae8d354cd76ae1c1110a658">xPSR_Type</a>
</li>
<li>TCR
: <a class="el" href="struct_i_t_m___type.html#a58f169e1aa40a9b8afb6296677c3bb45">ITM_Type</a>
</li>
<li>TER
: <a class="el" href="struct_i_t_m___type.html#a91a040e1b162e1128ac1e852b4a0e589">ITM_Type</a>
</li>
<li>TPR
: <a class="el" href="struct_i_t_m___type.html#a93b480aac6da620bbb611212186d47fa">ITM_Type</a>
</li>
<li>TRIGGER
: <a class="el" href="struct_t_p_i___type.html#aa4b603c71768dbda553da571eccba1fe">TPI_Type</a>
</li>
<li>TYPE
: <a class="el" href="struct_m_p_u___type.html#a6ae8a8c3a4909ae41447168d793608f7">MPU_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_u"></a>- u -</h3><ul>
<li>u16
: <a class="el" href="struct_i_t_m___type.html#a12aa4eb4d9dcb589a5d953c836f4e8f4">ITM_Type</a>
</li>
<li>u32
: <a class="el" href="struct_i_t_m___type.html#a6882fa5af67ef5c5dfb433b3b68939df">ITM_Type</a>
</li>
<li>u8
: <a class="el" href="struct_i_t_m___type.html#abea77b06775d325e5f6f46203f582433">ITM_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_v"></a>- v -</h3><ul>
<li>V
: <a class="el" href="union_a_p_s_r___type.html#a8004d224aacb78ca37774c35f9156e7e">APSR_Type</a>
, <a class="el" href="unionx_p_s_r___type.html#af14df16ea0690070c45b95f2116b7a0a">xPSR_Type</a>
</li>
<li>VAL
: <a class="el" href="struct_sys_tick___type.html#a0997ff20f11817f8246e8f0edac6f4e4">SysTick_Type</a>
</li>
<li>VTOR
: <a class="el" href="struct_s_c_b___type.html#a0faf96f964931cadfb71cfa54e051f6f">SCB_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_w"></a>- w -</h3><ul>
<li>w
: <a class="el" href="union_a_p_s_r___type.html#ae4c2ef8c9430d7b7bef5cbfbbaed3a94">APSR_Type</a>
, <a class="el" href="union_c_o_n_t_r_o_l___type.html#a6b642cca3d96da660b1198c133ca2a1f">CONTROL_Type</a>
, <a class="el" href="unionx_p_s_r___type.html#a1a47176768f45f79076c4f5b1b534bc2">xPSR_Type</a>
, <a class="el" href="union_i_p_s_r___type.html#a4adca999d3a0bc1ae682d73ea7cfa879">IPSR_Type</a>
</li>
</ul>


<h3><a class="anchor" id="index_z"></a>- z -</h3><ul>
<li>Z
: <a class="el" href="union_a_p_s_r___type.html#a3b04d58738b66a28ff13f23d8b0ba7e5">APSR_Type</a>
, <a class="el" href="unionx_p_s_r___type.html#a1e5d9801013d5146f2e02d9b7b3da562">xPSR_Type</a>
</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
