<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Change Log</title>
<title>CMSIS-DSP: Change Log</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_change_log_pg.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Change Log </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><hr/>
<p><b>Version 1.4.5 2015/03/19</b></p>
<p>Added support for the Cortex-M7 processor</p>
<p>Fixed bug in <a class="el" href="arm__mat__inverse__f32_8c.html">arm_mat_inverse_f32.c</a> and <a class="el" href="arm__mat__inverse__f64_8c.html">arm_mat_inverse_f64.c</a>. They weren't properly handling diagonal matrices.</p>
<p><a class="el" href="arm__cfft__f32_8c.html">arm_cfft_f32.c</a> - help documentation updated</p>
<p>Updated documentation to show deprecated functions</p>
<hr/>
 <b>Version 1.4.4 2014/07/31</b></p>
<p>Added the following new files:</p>
<ul>
<li><a class="el" href="arm__biquad__cascade__stereo__df2_t__f32_8c.html">arm_biquad_cascade_stereo_df2T_f32.c</a></li>
<li><a class="el" href="arm__biquad__cascade__stereo__df2_t__init__f32_8c.html">arm_biquad_cascade_stereo_df2T_init_f32.c</a></li>
<li><a class="el" href="arm__biquad__cascade__df2_t__f64_8c.html">arm_biquad_cascade_df2T_f64.c</a></li>
<li><a class="el" href="arm__biquad__cascade__df2_t__init__f64_8c.html">arm_biquad_cascade_df2T_init_f64.c</a></li>
<li><a class="el" href="arm__mat__inverse__f64_8c.html">arm_mat_inverse_f64.c</a></li>
<li><a class="el" href="arm__cfft__q15_8c.html">arm_cfft_q15.c</a></li>
<li><a class="el" href="arm__cfft__q31_8c.html">arm_cfft_q31.c</a></li>
</ul>
<p>Optimizations to the following files:</p>
<ul>
<li><a class="el" href="arm__biquad__cascade__df2_t__f32_8c.html">arm_biquad_cascade_df2T_f32.c</a></li>
<li><a class="el" href="arm__fir__f32_8c.html">arm_fir_f32.c</a></li>
<li><a class="el" href="arm__fir__fast__q31_8c.html">arm_fir_fast_q31.c</a></li>
<li><a class="el" href="arm__fir__q7_8c.html">arm_fir_q7.c</a></li>
<li><a class="el" href="arm__cfft__f32_8c.html">arm_cfft_f32.c</a></li>
<li><a class="el" href="arm__cfft__radix4__q31_8c.html">arm_cfft_radix4_q31.c</a></li>
<li><a class="el" href="arm__cfft__radix4__q15_8c.html">arm_cfft_radix4_q15.c</a></li>
<li><a class="el" href="arm__rfft__q31_8c.html">arm_rfft_q31.c</a></li>
<li><a class="el" href="arm__rfft__q15_8c.html">arm_rfft_q15.c</a></li>
</ul>
<hr/>
 <b>Version 1.4.3 2014/03/12</b></p>
<p>Undid changes to <a class="el" href="arm__biquad__cascade__df1__q31_8c.html">arm_biquad_cascade_df1_q31.c</a></p>
<p>Added support for COSMIC</p>
<p>Changed 'short' to 'q15_t' where appropriate</p>
<p>Fixed <a class="el" href="arm__conv__partial__fast__q15_8c.html">arm_conv_partial_fast_q15.c</a> for UNALIGNED_SUPPORT_DISABLE</p>
<p>Fixed <a class="el" href="arm__mat__cmplx__mult__q15_8c.html">arm_mat_cmplx_mult_q15.c</a> for UNALIGNED_SUPPORT_DISABLE</p>
<p>Fixed <a class="el" href="arm__conv__partial__opt__q7_8c.html">arm_conv_partial_opt_q7.c</a> for UNALIGNED_SUPPORT_DISABLE</p>
<p>Restored the internal fftlen of 16 to <a class="el" href="arm__rfft__fast__init__f32_8c.html">arm_rfft_fast_init_f32.c</a></p>
<p>Updated core_xxx.h files to newer versions from ARM</p>
<hr/>
 <b>Version 1.4.2 2013/10/16</b></p>
<p>Moved const structures from <a class="el" href="arm__const__structs_8h.html">arm_const_structs.h</a> to <a class="el" href="arm__const__structs_8c.html">arm_const_structs.c</a></p>
<p>Rfft_fast_f32 no longer allows fft length of 16 as it wouldn't have worked anyways</p>
<p>Partial convolution was producing the wrong results in some cases</p>
<p>arm_lms_q31 and q15 now saturate the results in the M0 code to match the M3 &amp; M4 code</p>
<p>Rfft_q15 and q31 had potential overflow issues resolved</p>
<p><a class="el" href="arm__biquad__cascade__df1__q31_8c.html">arm_biquad_cascade_df1_q31.c</a> had a typo which resulted in incorrect outputs</p>
<p>fast math sine and cosine now use linear interpolation</p>
<p>controller sin/cos now uses a more accurate interpolation algorithm</p>
<p>arm_mat_inverse was reading outside its input array</p>
<p>arm_cmplx_dot_prod was incorrect</p>
<p>switched some incorrect usages of __ssat to clip_q63_to_q31</p>
<p>changed var &amp; std q31 to downshift input data by 8</p>
<p>var q31 &amp; q15 no longer output larger data types</p>
<p><a class="el" href="arm__mat__cmplx__mult__q15_8c.html">arm_mat_cmplx_mult_q15.c</a> was done incorrectly for big vs little endian</p>
<p><a class="el" href="arm__mat__mult__q31_8c.html">arm_mat_mult_q31.c</a> was inconsistent with the other multiplies, so added saturation</p>
<p>arm_conv_partial_q15 had an incorrect comparison between signed &amp; unsigned values</p>
<hr/>
 <b>Version 1.4.1 2013/02/20</b></p>
<p>Updated licenses in headers to 2013</p>
<p>Fixed ALIGN4 macro in <a class="el" href="arm__math_8h.html">arm_math.h</a></p>
<p>Added files to Cortex-M0 projects so that all projects have same file list</p>
<p>Fixed bugs in</p>
<ul>
<li>arm_biquad_cascade_d2fT_f32.c</li>
<li>arm_cfft_radix2_q31</li>
<li>arm_cfft_radix2_f32</li>
<li><a class="el" href="arm__math_8h.html">arm_math.h</a> (arm_pid functions)</li>
<li><a class="el" href="arm__iir__lattice__q31_8c.html">arm_iir_lattice_q31.c</a></li>
</ul>
<hr/>
 <b>Version 1.4.0 2013/01/09</b></p>
<p>Updated with more optimizations, bug fixes and new license information in headers</p>
<p>Optimized functions:</p>
<ul>
<li>arm_biquad_cascade_df2T_f32</li>
<li>arm_biquad_cascade_df1_q31</li>
<li>arm_fir_f32</li>
<li>arm_fir_fast_q31</li>
<li>arm_cfft_f32</li>
<li>arm_cfft_radix2_q31</li>
<li>arm_rfft_fast_f32 (new function)</li>
</ul>
<p>Fixed compiler warnings in <a class="el" href="arm__math_8h.html">arm_math.h</a> for comparing signed and unsigned ints</p>
<p>Fixed a saturation bug in arm_rms_q15</p>
<p>Simplified the code in arm_sin_cos_q31</p>
<p>Added a preprocessor directive to treat the Cortex M0+ just like the Cortex M0</p>
<p>The following functions were deprecated and will be removed in a future version</p>
<ul>
<li>arm_cfft_radix2_f32</li>
<li>arm_cfft_radix2_init_f32</li>
<li>arm_cfft_radix4_f32</li>
<li>arm_cfft_radix4_init_f32</li>
</ul>
<hr/>
 <b>Version 1.3.0</b></p>
<p>Added CMSIS DSP Software Library</p>
<p>The CMSIS DSP Software Library is a suite of common signal processing functions targeted to Cortex-M processor based microcontrollers. Even though the code has been specifically optimized towards using the extended DSP instruction set of the Cortex-M4 processor, the library can be compiled for any Cortex-M processor.</p>
<p>For more information please see CMSIS DSP Library documentation. Added Cortex-M4 Core Support</p>
<p>Additional folder CM4, containing the Cortex-M4 core support files, has been added. CM0 CM3 CM4 CoreSupport DeviceSupport</p>
<p>New naming for Core Support Files</p>
<p>The new Core Support Files are:</p>
<ul>
<li>core_cm#.h (# = 0, 3, 4)</li>
<li>core_cmFunc.h (Cortex-M Core Register access functions)</li>
<li>core_cmInstr.h (Cortex-M Core instructions)</li>
<li>core_cm4_simd.h (Cortex-M4 SIMD instructions)</li>
</ul>
<hr/>
 <b>Version 1.2.0</b></p>
<p>Removed CMSIS Middelware packages</p>
<p>CMSIS Middleware is on hold from ARM side until a agreement between all CMSIS partners is found. SystemFrequency renamed to SystemCoreClock</p>
<p>The variable name SystemCoreClock is more precise than SystemFrequency because the variable holds the clock value at which the core is running. Changed startup concept</p>
<p>The old startup concept (calling SystemInit_ExtMemCtl from startup file and calling SystemInit from main) has the weakness that it does not work for controllers which need a already configuerd clock system to configure the external memory controller.</p>
<p>Changed startup concept</p>
<ul>
<li><a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2" title="Setup the microcontroller system. Initialize the System.">SystemInit()</a> is called from startup file before premain.</li>
<li><a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2" title="Setup the microcontroller system. Initialize the System.">SystemInit()</a> configures the clock system and also configures an existing external memory controller.</li>
<li><a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#a93f514700ccf00d08dbdcff7f1224eb2" title="Setup the microcontroller system. Initialize the System.">SystemInit()</a> must not use global variables.</li>
<li>SystemCoreClock is initialized with a correct predefined value.</li>
<li>Additional function void SystemCoreClockUpdate (void) is provided.</li>
<li><a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">SystemCoreClockUpdate()</a> updates the variable SystemCoreClock and must be called whenever the core clock is changed.</li>
<li><a class="el" href="arm__class__marks__example_2_a_r_m_2_r_t_e_2_device_2_a_r_m_c_m0_2system___a_r_m_c_m0_8c.html#ae0c36a9591fe6e9c45ecb21a794f0f0f">SystemCoreClockUpdate()</a> evaluates the clock register settings and calculates the current core clock.</li>
</ul>
<p>Advanced Debug Functions</p>
<p>ITM communication channel is only capable for OUT direction. To allow also communication for IN direction a simple concept is provided.</p>
<ul>
<li>Global variable volatile int ITM_RxBuffer used for IN data.</li>
<li>Function int ITM_CheckChar (void) checks if a new character is available.</li>
<li>Function int ITM_ReceiveChar (void) retrieves the new character.</li>
</ul>
<p>For detailed explanation see file CMSIS debug support.htm.</p>
<p>Core Register Bit Definitions</p>
<p>Files core_cm3.h and core_cm0.h contain now bit definitions for Core Registers. The name for the defines correspond with the Cortex-M Technical Reference Manual.</p>
<p>e.g. SysTick structure with bit definitions</p>
<div class="fragment"><div class="line"></div>
<div class="line"><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line">{</div>
<div class="line">  __IO uint32_t CTRL;                         </div>
<div class="line">  __IO uint32_t LOAD;                         </div>
<div class="line">  __IO uint32_t VAL;                          </div>
<div class="line">  __I  uint32_t CALIB;                        </div>
<div class="line">} SysTick_Type;</div>
<div class="line"></div>
<div class="line"><span class="comment">/* SysTick Control / Status Register Definitions */</span></div>
<div class="line"><span class="preprocessor">#define SysTick_CTRL_COUNTFLAG_Pos     16                                      </span></div>
<div class="line"><span class="preprocessor">#define SysTick_CTRL_COUNTFLAG_Msk    (1ul &lt;&lt; SysTick_CTRL_COUNTFLAG_Pos)      </span></div>
<div class="line"><span class="preprocessor">#define SysTick_CTRL_CLKSOURCE_Pos      2                                      </span></div>
<div class="line"><span class="preprocessor">#define SysTick_CTRL_CLKSOURCE_Msk     (1ul &lt;&lt; SysTick_CTRL_CLKSOURCE_Pos)     </span></div>
<div class="line"><span class="preprocessor">#define SysTick_CTRL_TICKINT_Pos        1                                      </span></div>
<div class="line"><span class="preprocessor">#define SysTick_CTRL_TICKINT_Msk       (1ul &lt;&lt; SysTick_CTRL_TICKINT_Pos)       </span></div>
<div class="line"><span class="preprocessor">#define SysTick_CTRL_ENABLE_Pos         0                                      </span></div>
<div class="line"><span class="preprocessor">#define SysTick_CTRL_ENABLE_Msk        (1ul &lt;&lt; SysTick_CTRL_ENABLE_Pos)        </span></div>
<div class="line"><span class="preprocessor"></span><span class="comment">/* SysTick Reload Register Definitions */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define SysTick_LOAD_RELOAD_Pos         0                                      </span></div>
<div class="line"><span class="preprocessor">#define SysTick_LOAD_RELOAD_Msk        (0xFFFFFFul &lt;&lt; SysTick_LOAD_RELOAD_Pos) </span></div>
<div class="line"><span class="preprocessor"></span><span class="comment">/* SysTick Current Register Definitions */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define SysTick_VAL_CURRENT_Pos         0                                      </span></div>
<div class="line"><span class="preprocessor">#define SysTick_VAL_CURRENT_Msk        (0xFFFFFFul &lt;&lt; SysTick_VAL_CURRENT_Pos) </span></div>
<div class="line"><span class="preprocessor"></span><span class="comment">/* SysTick Calibration Register Definitions */</span><span class="preprocessor"></span></div>
<div class="line"><span class="preprocessor"></span><span class="preprocessor">#define SysTick_CALIB_NOREF_Pos        31                                      </span></div>
<div class="line"><span class="preprocessor">#define SysTick_CALIB_NOREF_Msk       (1ul &lt;&lt; SysTick_CALIB_NOREF_Pos)         </span></div>
<div class="line"><span class="preprocessor">#define SysTick_CALIB_SKEW_Pos         30                                      </span></div>
<div class="line"><span class="preprocessor">#define SysTick_CALIB_SKEW_Msk        (1ul &lt;&lt; SysTick_CALIB_SKEW_Pos)          </span></div>
<div class="line"><span class="preprocessor">#define SysTick_CALIB_TENMS_Pos         0                                      </span></div>
<div class="line"><span class="preprocessor">#define SysTick_CALIB_TENMS_Msk        (0xFFFFFFul &lt;&lt; SysTick_VAL_CURRENT_Pos) </span></div>
<div class="line"><span class="preprocessor"> </span><span class="comment">/* end of group CMSIS_CM3_SysTick */</span><span class="preprocessor"></span></div>
</div><!-- fragment --><p>DoxyGen Tags</p>
<p>DoxyGen tags in files core_cm3.[c,h] and core_cm0.[c,h] are reworked to create proper documentation using DoxyGen. Folder Structure</p>
<p>The folder structure is changed to differentiate the single support packages. </p>
<pre class="fragment">CM0
CM3
    CoreSupport
    DeviceSupport
        Vendor
            Device
                Startup
                    Toolchain
                    Toolchain
                    ...
            Device
            ...
        Vendor
        ...
    Example (optional)
        Toolchain
            Device
            Device
            ...
        Toolchain
        ...
Documentation
</pre><hr/>
 <b>Version 1.1.0 2012/02/15</b></p>
<p>Updated with more optimizations, bug fixes and minor API changes.</p>
<hr/>
 <b>Version 1.0.11 2011/10/18</b></p>
<p>Bug Fix in conv, correlation, partial convolution.</p>
<hr/>
 <b>Version 1.0.10 2011/7/15</b></p>
<p>Big Endian support added and Merged M0 and M3/M4 Source code.</p>
<hr/>
 <b>Version 1.0.3 2010/11/29</b></p>
<p>Re-organized the CMSIS folders and updated documentation.</p>
<hr/>
 <b>Version 1.0.2 2010/11/11</b></p>
<p>Documentation updated.</p>
<hr/>
 <b>Version 1.0.1 2010/10/05</b></p>
<p>Production release and review comments incorporated.</p>
<hr/>
 <b>Version 1.0.0 2010/09/20</b></p>
<p>Production release and review comments incorporated.</p>
<hr/>
 <b>Version 0.0.9 2010/08/27</b></p>
<p>Added files: <a class="el" href="arm__biquad__cascade__df1__fast__q15_8c.html">arm_biquad_cascade_df1_fast_q15.c</a> <a class="el" href="arm__biquad__cascade__df1__fast__q31_8c.html">arm_biquad_cascade_df1_fast_q31.c</a> <a class="el" href="arm__fir__fast__q31_8c.html">arm_fir_fast_q31.c</a> <a class="el" href="arm__fir__fast__q15_8c.html">arm_fir_fast_q15.c</a></p>
<hr/>
 <b>Version 0.0.7 2010/06/10</b></p>
<p>Misra-C changes done</p>
<hr/>
 <b>Version 0.0.5 2010/04/26</b></p>
<p>incorporated review comments and updated with latest CMSIS layer</p>
<hr/>
 <b>Version 0.0.3 2010/03/10 DP</b></p>
<p>Initial version </p>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:50 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
