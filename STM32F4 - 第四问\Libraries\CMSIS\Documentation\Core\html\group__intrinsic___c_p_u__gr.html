<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Intrinsic Functions for CPU Instructions</title>
<title>CMSIS-CORE: Intrinsic Functions for CPU Instructions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group__intrinsic___c_p_u__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Intrinsic Functions for CPU Instructions</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gac71fad9f0a91980fecafcb450ee0a63e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gac71fad9f0a91980fecafcb450ee0a63e">__NOP</a> (void)</td></tr>
<tr class="memdesc:gac71fad9f0a91980fecafcb450ee0a63e"><td class="mdescLeft">&#160;</td><td class="mdescRight">No Operation.  <a href="#gac71fad9f0a91980fecafcb450ee0a63e"></a><br/></td></tr>
<tr class="separator:gac71fad9f0a91980fecafcb450ee0a63e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaed91dfbf3d7d7b7fba8d912fcbeaad88"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gaed91dfbf3d7d7b7fba8d912fcbeaad88">__WFI</a> (void)</td></tr>
<tr class="memdesc:gaed91dfbf3d7d7b7fba8d912fcbeaad88"><td class="mdescLeft">&#160;</td><td class="mdescRight">Wait For Interrupt.  <a href="#gaed91dfbf3d7d7b7fba8d912fcbeaad88"></a><br/></td></tr>
<tr class="separator:gaed91dfbf3d7d7b7fba8d912fcbeaad88"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad3efec76c3bfa2b8528ded530386c563"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gad3efec76c3bfa2b8528ded530386c563">__WFE</a> (void)</td></tr>
<tr class="memdesc:gad3efec76c3bfa2b8528ded530386c563"><td class="mdescLeft">&#160;</td><td class="mdescRight">Wait For Event.  <a href="#gad3efec76c3bfa2b8528ded530386c563"></a><br/></td></tr>
<tr class="separator:gad3efec76c3bfa2b8528ded530386c563"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3c34da7eb16496ae2668a5b95fa441e7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga3c34da7eb16496ae2668a5b95fa441e7">__SEV</a> (void)</td></tr>
<tr class="memdesc:ga3c34da7eb16496ae2668a5b95fa441e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Send Event.  <a href="#ga3c34da7eb16496ae2668a5b95fa441e7"></a><br/></td></tr>
<tr class="separator:ga3c34da7eb16496ae2668a5b95fa441e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga92f5621626711931da71eaa8bf301af7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga92f5621626711931da71eaa8bf301af7">__BKPT</a> (uint8_t value)</td></tr>
<tr class="memdesc:ga92f5621626711931da71eaa8bf301af7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set Breakpoint.  <a href="#ga92f5621626711931da71eaa8bf301af7"></a><br/></td></tr>
<tr class="separator:ga92f5621626711931da71eaa8bf301af7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga93c09b4709394d81977300d5f84950e5"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga93c09b4709394d81977300d5f84950e5">__ISB</a> (void)</td></tr>
<tr class="memdesc:ga93c09b4709394d81977300d5f84950e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Instruction Synchronization Barrier.  <a href="#ga93c09b4709394d81977300d5f84950e5"></a><br/></td></tr>
<tr class="separator:ga93c09b4709394d81977300d5f84950e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacb2a8ca6eae1ba4b31161578b720c199"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gacb2a8ca6eae1ba4b31161578b720c199">__DSB</a> (void)</td></tr>
<tr class="memdesc:gacb2a8ca6eae1ba4b31161578b720c199"><td class="mdescLeft">&#160;</td><td class="mdescRight">Data Synchronization Barrier.  <a href="#gacb2a8ca6eae1ba4b31161578b720c199"></a><br/></td></tr>
<tr class="separator:gacb2a8ca6eae1ba4b31161578b720c199"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab1c9b393641dc2d397b3408fdbe72b96"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gab1c9b393641dc2d397b3408fdbe72b96">__DMB</a> (void)</td></tr>
<tr class="memdesc:gab1c9b393641dc2d397b3408fdbe72b96"><td class="mdescLeft">&#160;</td><td class="mdescRight">Data Memory Barrier.  <a href="#gab1c9b393641dc2d397b3408fdbe72b96"></a><br/></td></tr>
<tr class="separator:gab1c9b393641dc2d397b3408fdbe72b96"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4717abc17af5ba29b1e4c055e0a0d9b8"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga4717abc17af5ba29b1e4c055e0a0d9b8">__REV</a> (uint32_t value)</td></tr>
<tr class="memdesc:ga4717abc17af5ba29b1e4c055e0a0d9b8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reverse byte order (32 bit)  <a href="#ga4717abc17af5ba29b1e4c055e0a0d9b8"></a><br/></td></tr>
<tr class="separator:ga4717abc17af5ba29b1e4c055e0a0d9b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeef6f853b6df3a365c838ee5b49a7a26"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gaeef6f853b6df3a365c838ee5b49a7a26">__REV16</a> (uint32_t value)</td></tr>
<tr class="memdesc:gaeef6f853b6df3a365c838ee5b49a7a26"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reverse byte order (16 bit)  <a href="#gaeef6f853b6df3a365c838ee5b49a7a26"></a><br/></td></tr>
<tr class="separator:gaeef6f853b6df3a365c838ee5b49a7a26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ec006e6d79063363cb0c2a2e0b3adbe"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga1ec006e6d79063363cb0c2a2e0b3adbe">__REVSH</a> (int32_t value)</td></tr>
<tr class="memdesc:ga1ec006e6d79063363cb0c2a2e0b3adbe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reverse byte order in signed short value.  <a href="#ga1ec006e6d79063363cb0c2a2e0b3adbe"></a><br/></td></tr>
<tr class="separator:ga1ec006e6d79063363cb0c2a2e0b3adbe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad6f9f297f6b91a995ee199fbc796b863"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gad6f9f297f6b91a995ee199fbc796b863">__RBIT</a> (uint32_t value)</td></tr>
<tr class="memdesc:gad6f9f297f6b91a995ee199fbc796b863"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reverse bit order of value [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#gad6f9f297f6b91a995ee199fbc796b863"></a><br/></td></tr>
<tr class="separator:gad6f9f297f6b91a995ee199fbc796b863"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf66beb577bb9d90424c3d1d7f684c024"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gaf66beb577bb9d90424c3d1d7f684c024">__ROR</a> (uint32_t value, uint32_t shift)</td></tr>
<tr class="memdesc:gaf66beb577bb9d90424c3d1d7f684c024"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate a value right by a number of bits.  <a href="#gaf66beb577bb9d90424c3d1d7f684c024"></a><br/></td></tr>
<tr class="separator:gaf66beb577bb9d90424c3d1d7f684c024"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9e3ac13d8dcf4331176b624cf6234a7e"><td class="memItemLeft" align="right" valign="top">uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga9e3ac13d8dcf4331176b624cf6234a7e">__LDREXB</a> (volatile uint8_t *addr)</td></tr>
<tr class="memdesc:ga9e3ac13d8dcf4331176b624cf6234a7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">LDR Exclusive (8 bit) [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga9e3ac13d8dcf4331176b624cf6234a7e"></a><br/></td></tr>
<tr class="separator:ga9e3ac13d8dcf4331176b624cf6234a7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9feffc093d6f68b120d592a7a0d45a15"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga9feffc093d6f68b120d592a7a0d45a15">__LDREXH</a> (volatile uint16_t *addr)</td></tr>
<tr class="memdesc:ga9feffc093d6f68b120d592a7a0d45a15"><td class="mdescLeft">&#160;</td><td class="mdescRight">LDR Exclusive (16 bit) [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga9feffc093d6f68b120d592a7a0d45a15"></a><br/></td></tr>
<tr class="separator:ga9feffc093d6f68b120d592a7a0d45a15"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabd78840a0f2464905b7cec791ebc6a4c"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gabd78840a0f2464905b7cec791ebc6a4c">__LDREXW</a> (volatile uint32_t *addr)</td></tr>
<tr class="memdesc:gabd78840a0f2464905b7cec791ebc6a4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">LDR Exclusive (32 bit) [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#gabd78840a0f2464905b7cec791ebc6a4c"></a><br/></td></tr>
<tr class="separator:gabd78840a0f2464905b7cec791ebc6a4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaab6482d1f59f59e2b6b7efc1af391c99"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gaab6482d1f59f59e2b6b7efc1af391c99">__STREXB</a> (uint8_t value, volatile uint8_t *addr)</td></tr>
<tr class="memdesc:gaab6482d1f59f59e2b6b7efc1af391c99"><td class="mdescLeft">&#160;</td><td class="mdescRight">STR Exclusive (8 bit) [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#gaab6482d1f59f59e2b6b7efc1af391c99"></a><br/></td></tr>
<tr class="separator:gaab6482d1f59f59e2b6b7efc1af391c99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0a354bdf71caa52f081a4a54e84c8d2a"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga0a354bdf71caa52f081a4a54e84c8d2a">__STREXH</a> (uint16_t value, volatile uint16_t *addr)</td></tr>
<tr class="memdesc:ga0a354bdf71caa52f081a4a54e84c8d2a"><td class="mdescLeft">&#160;</td><td class="mdescRight">STR Exclusive (16 bit) [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga0a354bdf71caa52f081a4a54e84c8d2a"></a><br/></td></tr>
<tr class="separator:ga0a354bdf71caa52f081a4a54e84c8d2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga335deaaa7991490e1450cb7d1e4c5197"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga335deaaa7991490e1450cb7d1e4c5197">__STREXW</a> (uint32_t value, volatile uint32_t *addr)</td></tr>
<tr class="memdesc:ga335deaaa7991490e1450cb7d1e4c5197"><td class="mdescLeft">&#160;</td><td class="mdescRight">STR Exclusive (32 bit) [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga335deaaa7991490e1450cb7d1e4c5197"></a><br/></td></tr>
<tr class="separator:ga335deaaa7991490e1450cb7d1e4c5197"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga354c5ac8870cc3dfb823367af9c4b412"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga354c5ac8870cc3dfb823367af9c4b412">__CLREX</a> (void)</td></tr>
<tr class="memdesc:ga354c5ac8870cc3dfb823367af9c4b412"><td class="mdescLeft">&#160;</td><td class="mdescRight">Remove the exclusive lock [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga354c5ac8870cc3dfb823367af9c4b412"></a><br/></td></tr>
<tr class="separator:ga354c5ac8870cc3dfb823367af9c4b412"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7d9dddda18805abbf51ac21c639845e1"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga7d9dddda18805abbf51ac21c639845e1">__SSAT</a> (unint32_t value, uint32_t sat)</td></tr>
<tr class="memdesc:ga7d9dddda18805abbf51ac21c639845e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed Saturate [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga7d9dddda18805abbf51ac21c639845e1"></a><br/></td></tr>
<tr class="separator:ga7d9dddda18805abbf51ac21c639845e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76bbe4374a5912362866cdc1ded4064a"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga76bbe4374a5912362866cdc1ded4064a">__USAT</a> (uint32_t value, uint32_t sat)</td></tr>
<tr class="memdesc:ga76bbe4374a5912362866cdc1ded4064a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned Saturate [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga76bbe4374a5912362866cdc1ded4064a"></a><br/></td></tr>
<tr class="separator:ga76bbe4374a5912362866cdc1ded4064a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga90884c591ac5d73d6069334eba9d6c02"><td class="memItemLeft" align="right" valign="top">uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga90884c591ac5d73d6069334eba9d6c02">__CLZ</a> (uint32_t value)</td></tr>
<tr class="memdesc:ga90884c591ac5d73d6069334eba9d6c02"><td class="mdescLeft">&#160;</td><td class="mdescRight">Count leading zeros [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga90884c591ac5d73d6069334eba9d6c02"></a><br/></td></tr>
<tr class="separator:ga90884c591ac5d73d6069334eba9d6c02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac09134f1bf9c49db07282001afcc9380"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gac09134f1bf9c49db07282001afcc9380">__RRX</a> (uint32_t value)</td></tr>
<tr class="memdesc:gac09134f1bf9c49db07282001afcc9380"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate Right with Extend (32 bit)  <a href="#gac09134f1bf9c49db07282001afcc9380"></a><br/></td></tr>
<tr class="separator:gac09134f1bf9c49db07282001afcc9380"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9464d75db32846aa8295c3c3adfacb41"><td class="memItemLeft" align="right" valign="top">uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga9464d75db32846aa8295c3c3adfacb41">__LDRBT</a> (uint8_t ptr)</td></tr>
<tr class="memdesc:ga9464d75db32846aa8295c3c3adfacb41"><td class="mdescLeft">&#160;</td><td class="mdescRight">LDRT Unprivileged (8 bit)  <a href="#ga9464d75db32846aa8295c3c3adfacb41"></a><br/></td></tr>
<tr class="separator:ga9464d75db32846aa8295c3c3adfacb41"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa762b8bc5634ce38cb14d62a6b2aee32"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gaa762b8bc5634ce38cb14d62a6b2aee32">__LDRHT</a> (uint16_t ptr)</td></tr>
<tr class="memdesc:gaa762b8bc5634ce38cb14d62a6b2aee32"><td class="mdescLeft">&#160;</td><td class="mdescRight">LDRT Unprivileged (16 bit)  <a href="#gaa762b8bc5634ce38cb14d62a6b2aee32"></a><br/></td></tr>
<tr class="separator:gaa762b8bc5634ce38cb14d62a6b2aee32"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga616504f5da979ba8a073d428d6e8d5c7"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga616504f5da979ba8a073d428d6e8d5c7">__LDRT</a> (uint32_t ptr)</td></tr>
<tr class="memdesc:ga616504f5da979ba8a073d428d6e8d5c7"><td class="mdescLeft">&#160;</td><td class="mdescRight">LDRT Unprivileged (32 bit)  <a href="#ga616504f5da979ba8a073d428d6e8d5c7"></a><br/></td></tr>
<tr class="separator:ga616504f5da979ba8a073d428d6e8d5c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad41aa59c92c0a165b7f98428d3320cd5"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#gad41aa59c92c0a165b7f98428d3320cd5">__STRBT</a> (uint8_t value, uint8_t ptr)</td></tr>
<tr class="memdesc:gad41aa59c92c0a165b7f98428d3320cd5"><td class="mdescLeft">&#160;</td><td class="mdescRight">STRT Unprivileged (8 bit)  <a href="#gad41aa59c92c0a165b7f98428d3320cd5"></a><br/></td></tr>
<tr class="separator:gad41aa59c92c0a165b7f98428d3320cd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2b5d93b8e461755b1072a03df3f1722e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga2b5d93b8e461755b1072a03df3f1722e">__STRHT</a> (uint16_t value, uint16_t ptr)</td></tr>
<tr class="memdesc:ga2b5d93b8e461755b1072a03df3f1722e"><td class="mdescLeft">&#160;</td><td class="mdescRight">STRT Unprivileged (16 bit)  <a href="#ga2b5d93b8e461755b1072a03df3f1722e"></a><br/></td></tr>
<tr class="separator:ga2b5d93b8e461755b1072a03df3f1722e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga625bc4ac0b1d50de9bcd13d9f050030e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__intrinsic___c_p_u__gr.html#ga625bc4ac0b1d50de9bcd13d9f050030e">__STRT</a> (uint32_t value, uint32_t ptr)</td></tr>
<tr class="memdesc:ga625bc4ac0b1d50de9bcd13d9f050030e"><td class="mdescLeft">&#160;</td><td class="mdescRight">STRT Unprivileged (32 bit)  <a href="#ga625bc4ac0b1d50de9bcd13d9f050030e"></a><br/></td></tr>
<tr class="separator:ga625bc4ac0b1d50de9bcd13d9f050030e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>The following functions generate specific Cortex-M instructions that cannot be directly accessed by the C/C++ Compiler. Refer to the <a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> for detailed information about these Cortex-M instructions.</p>
<dl class="section note"><dt>Note</dt><dd>When using the ARM Compiler Toolchain the following <a class="el" href="group__intrinsic___c_p_u__gr.html">Intrinsic Functions for CPU Instructions</a> are implemented using the Embedded Assembler: <a class="el" href="group__intrinsic___c_p_u__gr.html#gac09134f1bf9c49db07282001afcc9380">__RRX</a>, &lt;Bruno: add more...&gt;. The usage of the Embedded Assembler can be disabled by with <b><em>define __NO_EMBEDDED_ASM</em></b>. This avoids potential side effects of the Embedded Assembler. Refer to <b>Compiler User Guide - Using the Inline and Embedded Assemblers of the ARM Compiler</b> for more information. </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga92f5621626711931da71eaa8bf301af7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __BKPT </td>
          <td>(</td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function causes the processor to enter Debug state. Debug tools can use this to investigate system state when the instruction at a particular address is reached.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>is ignored by the processor. If required, a debugger can use it to obtain additional information about the breakpoint. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga354c5ac8870cc3dfb823367af9c4b412"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __CLREX </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function removes the exclusive lock which is created by LDREX [not for Cortex-M0, Cortex-M0+, or SC000]. </p>

</div>
</div>
<a class="anchor" id="ga90884c591ac5d73d6069334eba9d6c02"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t __CLZ </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function counts the number of leading zeros of a data value [not for Cortex-M0, Cortex-M0+, or SC000].</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to count the leading zeros </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>number of leading zeros in value </dd></dl>

</div>
</div>
<a class="anchor" id="gab1c9b393641dc2d397b3408fdbe72b96"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __DMB </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function ensures the apparent order of the explicit memory operations before and after the instruction, without ensuring their completion. </p>

</div>
</div>
<a class="anchor" id="gacb2a8ca6eae1ba4b31161578b720c199"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __DSB </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function acts as a special kind of Data Memory Barrier. It completes when all explicit memory accesses before this instruction complete. </p>

</div>
</div>
<a class="anchor" id="ga93c09b4709394d81977300d5f84950e5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __ISB </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Instruction Synchronization Barrier flushes the pipeline in the processor, so that all instructions following the ISB are fetched from cache or memory, after the instruction has been completed. </p>

</div>
</div>
<a class="anchor" id="ga9464d75db32846aa8295c3c3adfacb41"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t __LDRBT </td>
          <td>(</td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function executed an Unprivileged LDRT command for 8 bit value.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">ptr</td><td>Pointer to data </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>value of type uint8_t at (*ptr) </dd></dl>

</div>
</div>
<a class="anchor" id="ga9e3ac13d8dcf4331176b624cf6234a7e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint8_t __LDREXB </td>
          <td>(</td>
          <td class="paramtype">volatile uint8_t *&#160;</td>
          <td class="paramname"><em>addr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function executed an exclusive LDR command for 8 bit value [not for Cortex-M0, Cortex-M0+, or SC000].</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*addr</td><td>Pointer to data </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>value of type uint8_t at (*addr) </dd></dl>

</div>
</div>
<a class="anchor" id="ga9feffc093d6f68b120d592a7a0d45a15"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t __LDREXH </td>
          <td>(</td>
          <td class="paramtype">volatile uint16_t *&#160;</td>
          <td class="paramname"><em>addr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function executed an exclusive LDR command for 16 bit values [not for Cortex-M0, Cortex-M0+, or SC000].</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*addr</td><td>Pointer to data </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>value of type uint16_t at (*addr) </dd></dl>

</div>
</div>
<a class="anchor" id="gabd78840a0f2464905b7cec791ebc6a4c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __LDREXW </td>
          <td>(</td>
          <td class="paramtype">volatile uint32_t *&#160;</td>
          <td class="paramname"><em>addr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function executed an exclusive LDR command for 32 bit values [not for Cortex-M0, Cortex-M0+, or SC000].</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*addr</td><td>Pointer to data </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>value of type uint32_t at (*addr) </dd></dl>

</div>
</div>
<a class="anchor" id="gaa762b8bc5634ce38cb14d62a6b2aee32"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t __LDRHT </td>
          <td>(</td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function executed an Unprivileged LDRT command for 16 bit values.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">ptr</td><td>Pointer to data </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>value of type uint16_t at (*ptr) </dd></dl>

</div>
</div>
<a class="anchor" id="ga616504f5da979ba8a073d428d6e8d5c7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __LDRT </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>ptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function executed an Unprivileged LDRT command for 32 bit values.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">ptr</td><td>Pointer to data </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>value of type uint32_t at (*ptr) </dd></dl>

</div>
</div>
<a class="anchor" id="gac71fad9f0a91980fecafcb450ee0a63e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __NOP </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function does nothing. This instruction can be used for code alignment purposes. </p>

</div>
</div>
<a class="anchor" id="gad6f9f297f6b91a995ee199fbc796b863"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __RBIT </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function reverses the bit order of the given value [not for Cortex-M0, Cortex-M0+, or SC000].</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to reverse </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Reversed value </dd></dl>

</div>
</div>
<a class="anchor" id="ga4717abc17af5ba29b1e4c055e0a0d9b8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __REV </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function reverses the byte order in integer value.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to reverse </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Reversed value </dd></dl>

</div>
</div>
<a class="anchor" id="gaeef6f853b6df3a365c838ee5b49a7a26"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __REV16 </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function reverses the byte order in two unsigned short values.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to reverse </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Reversed value </dd></dl>

</div>
</div>
<a class="anchor" id="ga1ec006e6d79063363cb0c2a2e0b3adbe"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t __REVSH </td>
          <td>(</td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function reverses the byte order in a signed short value with sign extension to integer.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to reverse </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Reversed value </dd></dl>

</div>
</div>
<a class="anchor" id="gaf66beb577bb9d90424c3d1d7f684c024"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __ROR </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>shift</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function rotates a value right by a specified number of bits.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to be shifted right </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">shift</td><td>Number of bits in the range [1..31] </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Rotated value </dd></dl>

</div>
</div>
<a class="anchor" id="gac09134f1bf9c49db07282001afcc9380"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __RRX </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function moves each bit of a bitstring right by one bit. The carry input is shifted in at the left end of the bitstring.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to rotate </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Rotated value </dd></dl>

</div>
</div>
<a class="anchor" id="ga3c34da7eb16496ae2668a5b95fa441e7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __SEV </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Send Event is a hint instruction. It causes an event to be signaled to the CPU. </p>

</div>
</div>
<a class="anchor" id="ga7d9dddda18805abbf51ac21c639845e1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __SSAT </td>
          <td>(</td>
          <td class="paramtype">unint32_t&#160;</td>
          <td class="paramname"><em>value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>sat</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function saturates a signed value [not for Cortex-M0, Cortex-M0+, or SC000].</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to be saturated </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">sat</td><td>Bit position to saturate to [1..32] </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Saturated value </dd></dl>

</div>
</div>
<a class="anchor" id="gad41aa59c92c0a165b7f98428d3320cd5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __STRBT </td>
          <td>(</td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>ptr</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function executed an Unprivileged STRT command for 8 bit values.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to store </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ptr</td><td>Pointer to location </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaab6482d1f59f59e2b6b7efc1af391c99"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __STREXB </td>
          <td>(</td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">volatile uint8_t *&#160;</td>
          <td class="paramname"><em>addr</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function executed an exclusive STR command for 8 bit values [not for Cortex-M0, Cortex-M0+, or SC000].</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to store </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*addr</td><td>Pointer to location </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>0 Function succeeded </dd>
<dd>
1 Function failed </dd></dl>

</div>
</div>
<a class="anchor" id="ga0a354bdf71caa52f081a4a54e84c8d2a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __STREXH </td>
          <td>(</td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">volatile uint16_t *&#160;</td>
          <td class="paramname"><em>addr</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function executed an exclusive STR command for 16 bit values [not for Cortex-M0, Cortex-M0+, or SC000].</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to store </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*addr</td><td>Pointer to location </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>0 Function succeeded </dd>
<dd>
1 Function failed </dd></dl>

</div>
</div>
<a class="anchor" id="ga335deaaa7991490e1450cb7d1e4c5197"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __STREXW </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">volatile uint32_t *&#160;</td>
          <td class="paramname"><em>addr</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function executed an exclusive STR command for 32 bit values [not for Cortex-M0, Cortex-M0+, or SC000].</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to store </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*addr</td><td>Pointer to location </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>0 Function succeeded </dd>
<dd>
1 Function failed </dd></dl>

</div>
</div>
<a class="anchor" id="ga2b5d93b8e461755b1072a03df3f1722e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __STRHT </td>
          <td>(</td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>ptr</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function executed an Unprivileged STRT command for 16 bit values.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to store </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ptr</td><td>Pointer to location </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga625bc4ac0b1d50de9bcd13d9f050030e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __STRT </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>ptr</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function executed an Unprivileged STRT command for 32 bit values.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to store </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ptr</td><td>Pointer to location </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga76bbe4374a5912362866cdc1ded4064a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t __USAT </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>sat</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function saturates an unsigned value [not for Cortex-M0, Cortex-M0+, or SC000].</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>Value to be saturated </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">sat</td><td>Bit position to saturate to [0..31] </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Saturated value </dd></dl>

</div>
</div>
<a class="anchor" id="gad3efec76c3bfa2b8528ded530386c563"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __WFE </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Wait For Event is a hint instruction that permits the processor to enter a low-power state until an events occurs: </p>
<ul>
<li>If the <b>event register is 0</b>, then WFE suspends execution until one of the following events occurs:<ul>
<li>An exception, unless masked by the exception mask registers or the current priority level.</li>
<li>An exception enters the Pending state, if SEVONPEND in the System Control Register is set.</li>
<li>A Debug Entry request, if Debug is enabled.</li>
<li>An event signaled by a peripheral or another processor in a multiprocessor system using the SEV instruction.</li>
</ul>
</li>
</ul>
<ul>
<li>If the <b>event register is 1</b>, then WFE clears it to 0 and returns immediately. </li>
</ul>

</div>
</div>
<a class="anchor" id="gaed91dfbf3d7d7b7fba8d912fcbeaad88"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __WFI </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>WFI is a hint instruction that suspends execution until one of the following events occurs:</p>
<ul>
<li>A non-masked interrupt occurs and is taken.</li>
<li>An interrupt masked by PRIMASK becomes pending.</li>
<li>A Debug Entry request. </li>
</ul>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
