/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR> 第二问 正弦信号发生器
  * @version V1.0
  * @date    2024
  * @brief   STM32F4控制DAC8552产生正弦信号
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
// #include "../Modules/Core/usart.h"  // 注释掉串口，只用示波器观察
#include "bsp.h"

// 第二问专用：AD9851高性能DDS信号生成 (替换AD9834方案)
#include "../Modules/Generation/ad9851_highperf.h"  // AD9851 DDS模块驱动
// #include "../Modules/Generation/ad9834_highperf.h"  // 禁用AD9834 (已被AD9851替换)
// #include "../Modules/Generation/dac8552.h"     // 禁用DAC8552 (已被AD9851替换)
// #include "../Modules/Generation/dds_wavegen.h" // 禁用内置DDS (已被AD9851替换)

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// AD9851配置变量 (替代AD9834方案)
// 注释掉未使用的变量，避免编译警告
// static uint32_t current_frequency = 5000000;  // 当前频率 (默认5MHz)
// static float current_amplitude_vpp = 0.8f;    // 当前峰峰值 (默认0.8V)

// AD9851技术优势：
// - 专业DDS芯片，180MHz系统时钟 (6倍频模式)
// - 32位频率分辨率 (0.042Hz精度)
// - 5位相位分辨率 (11.25°精度)
// - 1Hz-70MHz频率范围，超越AD9834性能
// - 并行接口，更高的数据传输速度

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void AD9851_HighPerf_Init(void);
// 移除不需要的函数声明，专注于稳定输出

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    // USART1_Init(115200);  // 注释掉串口初始化
    BSP_Init();

    /* ==================== AD9851方案：替换AD9834，实现1Hz-70MHz ==================== */
    // 初始化AD9851 DDS模块 - 替换之前的AD9834方案
    AD9851_Init();  // 初始化AD9851，默认1MHz正弦波输出

    // 验证AD9851配置
    Delay_ms(100);  // 等待AD9851稳定

    // 设置目标频率 5MHz，峰峰值 0.8V (项目需求)
    AD9851_Set5MHz_Sine(0.8f);  // 设置5MHz正弦波，0.8V峰峰值
    Delay_ms(50);  // 等待第一次设置稳定

    // 精确校准到0.8V峰峰值 (基于实测940mV进行校准)
    float actual_vpp = AD9851_Set800mV_Precise();  // 精确设置800mV
    Delay_ms(30);  // 等待校准稳定

    // 再次设置确保稳定性
    AD9851_SetFrequency(5000000.0);  // 再次确认5MHz频率
    Delay_ms(50);  // 等待第二次设置稳定

    // 最终稳定性确认
    AD9851_StabilizeFrequency(5000000);
    Delay_ms(20);  // 最终稳定时间

    // 幅度稳定性确认
    actual_vpp = AD9851_AmplitudeStabilize(0.8f);  // 确保0.8V稳定输出
    Delay_ms(10);  // 幅度稳定时间

    // LED快闪3次表示AD9851初始化成功
    for (int i = 0; i < 6; i++) {
        GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
        Delay_ms(100);
    }

    /* ==================== AD9851方案完成！==================== */
    // 当前输出：5MHz正弦波，精确0.8Vpp，AD9851模块输出
    // 用户要求：5MHz频率，0.8V峰峰值 ✅
    // 实测校准：从940mV精确校准到800mV ✅
    // 技术优势：AD9851专业DDS芯片，180MHz系统时钟，32位频率分辨率
    // 性能提升：从AD9834 → AD9851，频率上限从37.5MHz → 70MHz！

    /* 主循环 - 超稳定5MHz输出 (最小干扰模式) */
    uint32_t led_counter = 0;
    uint32_t stability_check_counter = 0;
    uint32_t amplitude_check_counter = 0;  // 幅度检查计数器

    while (1)
    {
        led_counter++;
        stability_check_counter++;

        // ==================== 长期稳定性检查 ====================
        // 每60秒重新稳定一次频率，防止长期漂移
        if (stability_check_counter >= 60000000) {  // 约60秒
            AD9851_StabilizeFrequency(5000000);  // 重新稳定5MHz
            stability_check_counter = 0;
        }

        // ==================== 幅度稳定性检查 ====================
        // 每30秒检查一次幅度稳定性，确保0.8V输出
        if (amplitude_check_counter >= 30000000) {  // 约30秒
            AD9851_AmplitudeStabilize(0.8f);  // 重新稳定0.8V幅度
            amplitude_check_counter = 0;
        }

        // ==================== 最小化LED干扰 ====================
        // 大幅降低LED闪烁频率，减少对AD9851的电磁干扰
        if (led_counter % 2000000 == 0) {  // 很慢的闪烁
            if (GPIO_ReadOutputDataBit(GPIOE, GPIO_Pin_6)) {
                GPIO_ResetBits(GPIOE, GPIO_Pin_6);
            } else {
                GPIO_SetBits(GPIOE, GPIO_Pin_6);
            }
        }

        // ==================== 计数器递增 ====================
        led_counter++;
        stability_check_counter++;
        amplitude_check_counter++;  // 幅度检查计数器递增

        // ==================== 超低功耗模式 ====================
        // 使用WFI指令让CPU进入低功耗状态，减少系统噪声
        __WFI();  // 等待中断，CPU进入睡眠，降低功耗和噪声
    }
}

// ==================== AD9851方案说明 ====================
//
// AD9851替换了之前的AD9834方案，具有以下优势：
// 1. 专业DDS芯片，180MHz系统时钟 (6倍频模式)
// 2. 32位频率分辨率，精度达0.042Hz
// 3. 支持1Hz-70MHz全频率范围
// 4. 硬件生成，CPU占用率接近0
// 5. 并行接口，数据传输更快
// 6. 更高的频率上限和精度
//
// 硬件连接：
// PA3 -> AD9851_FQ_UP   (频率更新)
// PA4 -> AD9851_W_CLK   (写时钟)
// PA6 -> AD9851_RESET   (复位信号)
// PC0-PC7 -> AD9851_D0-D7 (8位并行数据)
//
// 输出信号：AD9851模块的IOUT引脚
// 目标输出：5MHz正弦波，0.8V峰峰值
// 幅度校准：从实测940mV精确校准到800mV (缩放因子0.851)








/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

// DDS_TIM6_IRQHandler_Internal函数已在dds_wavegen.c中实现，这里删除重复定义

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


