<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>RealFFT</title>
<title>CMSIS-DSP: RealFFT</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___real_f_f_t.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">RealFFT</div>  </div>
<div class="ingroups"><a class="el" href="group__group_transforms.html">Transform Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga3df1766d230532bc068fc4ed69d0fcdc"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#ga3df1766d230532bc068fc4ed69d0fcdc">arm_rfft_f32</a> (const <a class="el" href="structarm__rfft__instance__f32.html">arm_rfft_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrc, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pDst)</td></tr>
<tr class="memdesc:ga3df1766d230532bc068fc4ed69d0fcdc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the floating-point RFFT/RIFFT.  <a href="#ga3df1766d230532bc068fc4ed69d0fcdc"></a><br/></td></tr>
<tr class="separator:ga3df1766d230532bc068fc4ed69d0fcdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga180d8b764d59cbb85d37a2d5f7cd9799"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#ga180d8b764d59cbb85d37a2d5f7cd9799">arm_rfft_fast_f32</a> (<a class="el" href="structarm__rfft__fast__instance__f32.html">arm_rfft_fast_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *p, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pOut, uint8_t <a class="el" href="_g_c_c_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>)</td></tr>
<tr class="memdesc:ga180d8b764d59cbb85d37a2d5f7cd9799"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the floating-point real FFT.  <a href="#ga180d8b764d59cbb85d37a2d5f7cd9799"></a><br/></td></tr>
<tr class="separator:ga180d8b764d59cbb85d37a2d5f7cd9799"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac5fceb172551e7c11eb4d0e17ef15aa3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32</a> (<a class="el" href="structarm__rfft__fast__instance__f32.html">arm_rfft_fast_instance_f32</a> *S, uint16_t fftLen)</td></tr>
<tr class="memdesc:gac5fceb172551e7c11eb4d0e17ef15aa3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point real FFT.  <a href="#gac5fceb172551e7c11eb4d0e17ef15aa3"></a><br/></td></tr>
<tr class="separator:gac5fceb172551e7c11eb4d0e17ef15aa3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga10717ee326bf50832ef1c25b85a23068"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32</a> (<a class="el" href="structarm__rfft__instance__f32.html">arm_rfft_instance_f32</a> *S, <a class="el" href="structarm__cfft__radix4__instance__f32.html">arm_cfft_radix4_instance_f32</a> *S_CFFT, uint32_t fftLenReal, uint32_t ifftFlagR, uint32_t bitReverseFlag)</td></tr>
<tr class="memdesc:ga10717ee326bf50832ef1c25b85a23068"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point RFFT/RIFFT.  <a href="#ga10717ee326bf50832ef1c25b85a23068"></a><br/></td></tr>
<tr class="separator:ga10717ee326bf50832ef1c25b85a23068"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga053450cc600a55410ba5b5605e96245d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15</a> (<a class="el" href="structarm__rfft__instance__q15.html">arm_rfft_instance_q15</a> *S, uint32_t fftLenReal, uint32_t ifftFlagR, uint32_t bitReverseFlag)</td></tr>
<tr class="memdesc:ga053450cc600a55410ba5b5605e96245d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q15 RFFT/RIFFT.  <a href="#ga053450cc600a55410ba5b5605e96245d"></a><br/></td></tr>
<tr class="separator:ga053450cc600a55410ba5b5605e96245d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5abde938abbe72e95c5bab080eb33c45"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31</a> (<a class="el" href="structarm__rfft__instance__q31.html">arm_rfft_instance_q31</a> *S, uint32_t fftLenReal, uint32_t ifftFlagR, uint32_t bitReverseFlag)</td></tr>
<tr class="memdesc:ga5abde938abbe72e95c5bab080eb33c45"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q31 RFFT/RIFFT.  <a href="#ga5abde938abbe72e95c5bab080eb33c45"></a><br/></td></tr>
<tr class="separator:ga5abde938abbe72e95c5bab080eb33c45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga00e615f5db21736ad5b27fb6146f3fc5"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#ga00e615f5db21736ad5b27fb6146f3fc5">arm_rfft_q15</a> (const <a class="el" href="structarm__rfft__instance__q15.html">arm_rfft_instance_q15</a> *S, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrc, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst)</td></tr>
<tr class="memdesc:ga00e615f5db21736ad5b27fb6146f3fc5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q15 RFFT/RIFFT.  <a href="#ga00e615f5db21736ad5b27fb6146f3fc5"></a><br/></td></tr>
<tr class="separator:ga00e615f5db21736ad5b27fb6146f3fc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabaeab5646aeea9844e6d42ca8c73fe3a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#gabaeab5646aeea9844e6d42ca8c73fe3a">arm_rfft_q31</a> (const <a class="el" href="structarm__rfft__instance__q31.html">arm_rfft_instance_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pDst)</td></tr>
<tr class="memdesc:gabaeab5646aeea9844e6d42ca8c73fe3a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q31 RFFT/RIFFT.  <a href="#gabaeab5646aeea9844e6d42ca8c73fe3a"></a><br/></td></tr>
<tr class="separator:gabaeab5646aeea9844e6d42ca8c73fe3a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:ga8b1ad947c470596674fa3364e16045c6"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#ga8b1ad947c470596674fa3364e16045c6">realCoefA</a> [8192]</td></tr>
<tr class="separator:ga8b1ad947c470596674fa3364e16045c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac52f98b52a1f03bfac8b57a67ba07397"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#gac52f98b52a1f03bfac8b57a67ba07397">realCoefB</a> [8192]</td></tr>
<tr class="separator:gac52f98b52a1f03bfac8b57a67ba07397"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga11e84d0ee257a547f749b37dd0078d36"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#ga11e84d0ee257a547f749b37dd0078d36">realCoefAQ15</a> [8192]</td></tr>
<tr class="separator:ga11e84d0ee257a547f749b37dd0078d36"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac871666f018b70938b2b98017628cb97"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#gac871666f018b70938b2b98017628cb97">realCoefBQ15</a> [8192]</td></tr>
<tr class="separator:gac871666f018b70938b2b98017628cb97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf1592a6cf0504675205074a43c3728a2"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#gaf1592a6cf0504675205074a43c3728a2">realCoefAQ31</a> [8192]</td></tr>
<tr class="separator:gaf1592a6cf0504675205074a43c3728a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1eb5745728a61c3715755f5d69a4a960"><td class="memItemLeft" align="right" valign="top">static const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___real_f_f_t.html#ga1eb5745728a61c3715755f5d69a4a960">realCoefBQ31</a> [8192]</td></tr>
<tr class="separator:ga1eb5745728a61c3715755f5d69a4a960"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga3df1766d230532bc068fc4ed69d0fcdc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_rfft_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__rfft__instance__f32.html">arm_rfft_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000013">Deprecated:</a></b></dt><dd>Do not use this function. It has been superceded by <a class="el" href="group___real_f_f_t.html#ga180d8b764d59cbb85d37a2d5f7cd9799">arm_rfft_fast_f32</a> and will be removed in the future. </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point RFFT/RIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the input buffer. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the output buffer. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__bitreversal_8c.html#a3d4062fdfa6aaa3f51f41cab868e508b">arm_bitreversal_f32()</a>, <a class="el" href="group__group_transforms.html#gae239ddf995d1607115f9e84d5c069b9c">arm_radix4_butterfly_f32()</a>, <a class="el" href="arm__cfft__radix4__f32_8c.html#a2a78df6e4bbf080624f2b6349224ec93">arm_radix4_butterfly_inverse_f32()</a>, <a class="el" href="group__group_transforms.html#ga6cfdb6bdc66b13732ef2351caf98fdbb">arm_split_rfft_f32()</a>, <a class="el" href="arm__rfft__f32_8c.html#a585bef78c103d150a116241a4feb6442">arm_split_rifft_f32()</a>, <a class="el" href="structarm__rfft__instance__f32.html#ac342f3248157cbbd2f04a3c8ec9fc9eb">arm_rfft_instance_f32::bitReverseFlagR</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#acc8cb18a8b901b8321ab9d86491e41a3">arm_cfft_radix4_instance_f32::bitRevFactor</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a7e6a6d290ce158ce9a15a45e364b021a">arm_cfft_radix4_instance_f32::fftLen</a>, <a class="el" href="structarm__rfft__instance__f32.html#a075076e07ebb8521d8e3b49a31db6c57">arm_rfft_instance_f32::fftLenBy2</a>, <a class="el" href="structarm__rfft__instance__f32.html#a5ee6d10a934ab4b666e0bb286c3d633f">arm_rfft_instance_f32::ifftFlagR</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#ab9eed39e40b8d7c16381fbccf84467cd">arm_cfft_radix4_instance_f32::onebyfftLen</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a8da0d2ca69749fde8cbb95caeac6fe6a">arm_cfft_radix4_instance_f32::pBitRevTable</a>, <a class="el" href="structarm__rfft__instance__f32.html#a9f47ba9f50c81e4445ae3827b981bc05">arm_rfft_instance_f32::pCfft</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#a14860c7544911702ca1fa0bf78204ef3">arm_cfft_radix4_instance_f32::pTwiddle</a>, <a class="el" href="structarm__rfft__instance__f32.html#a534cc7e6e9b3e3dd022fad611c762142">arm_rfft_instance_f32::pTwiddleAReal</a>, <a class="el" href="structarm__rfft__instance__f32.html#a23543ecfd027fea2477fe1eea23c3c4d">arm_rfft_instance_f32::pTwiddleBReal</a>, <a class="el" href="structarm__cfft__radix4__instance__f32.html#abe31ea2157dfa233e389cdfd3b9993ee">arm_cfft_radix4_instance_f32::twidCoefModifier</a>, and <a class="el" href="structarm__rfft__instance__f32.html#aede85350fb5ae6baa1b3e8bfa15b18d6">arm_rfft_instance_f32::twidCoefRModifier</a>.</p>

</div>
</div>
<a class="anchor" id="ga180d8b764d59cbb85d37a2d5f7cd9799"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_rfft_fast_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__rfft__fast__instance__f32.html">arm_rfft_fast_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>p</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pOut</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t&#160;</td>
          <td class="paramname"><em>ifftFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an <a class="el" href="structarm__rfft__fast__instance__f32.html" title="Instance structure for the floating-point RFFT/RIFFT function.">arm_rfft_fast_instance_f32</a> structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*p</td><td>points to the input buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pOut</td><td>points to the output buffer. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlag</td><td>RFFT if flag is 0, RIFFT if flag is 1 </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_cfft_f32()</a>, <a class="el" href="structarm__cfft__instance__f32.html#acd8f9e9540e3dd348212726e5d6aaa95">arm_cfft_instance_f32::fftLen</a>, <a class="el" href="structarm__rfft__fast__instance__f32.html#aef06ab665041ec36f5b25d464f0cab14">arm_rfft_fast_instance_f32::fftLenRFFT</a>, <a class="el" href="arm__rfft__fast__f32_8c.html#a93258bc1e64a939a8ebd086367e459af">merge_rfft_f32()</a>, <a class="el" href="structarm__rfft__fast__instance__f32.html#a37419ababdfb3151b1891ae6bcd21012">arm_rfft_fast_instance_f32::Sint</a>, and <a class="el" href="arm__rfft__fast__f32_8c.html#a47157c5a53c8aac5e80fda31acf1f9cc">stage_rfft_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="gac5fceb172551e7c11eb4d0e17ef15aa3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_rfft_fast_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__rfft__fast__instance__f32.html">arm_rfft_fast_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>fftLen</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an <a class="el" href="structarm__rfft__fast__instance__f32.html" title="Instance structure for the floating-point RFFT/RIFFT function.">arm_rfft_fast_instance_f32</a> structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fftLen</td><td>length of the Real Sequence. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLen</code> is not a supported value.</dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>fftLen</code> Specifies length of RFFT/CIFFT process. Supported FFT Lengths are 32, 64, 128, 256, 512, 1024, 2048, 4096. </dd></dl>
<dl class="section user"><dt></dt><dd>This Function also initializes Twiddle factor table pointer and Bit reversal table pointer. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="arm__common__tables_8c.html#ae69b72fb0be5dab9a0ea76e9b6995cb6">armBitRevIndexTable1024</a>, <a class="el" href="arm__common__tables_8h.html#af3b3659a55efaf414757d15e6c0ea9cc">ARMBITREVINDEXTABLE1024_TABLE_LENGTH</a>, <a class="el" href="arm__common__tables_8c.html#a04711bbb245f2ac7202db666eaaf10f2">armBitRevIndexTable128</a>, <a class="el" href="arm__common__tables_8c.html#a5ab065857509fe5780d79fdcdce801cb">armBitRevIndexTable16</a>, <a class="el" href="arm__common__tables_8c.html#a68b7fcd07ae5433082e600dc7e7c7430">armBitRevIndexTable2048</a>, <a class="el" href="arm__common__tables_8h.html#a1137f42be79c5941e942b58e262b5225">ARMBITREVINDEXTABLE2048_TABLE_LENGTH</a>, <a class="el" href="arm__common__tables_8c.html#a77b17c8e7539af315c57de27610d8407">armBitRevIndexTable256</a>, <a class="el" href="arm__common__tables_8c.html#afae094ea3df14c134012c4cb7b816637">armBitRevIndexTable32</a>, <a class="el" href="arm__common__tables_8c.html#a297a311183fb6d17d7ee0152ad1e43f3">armBitRevIndexTable512</a>, <a class="el" href="arm__common__tables_8c.html#aafcb5c9203dada88ed6d1bdcf16aaba4">armBitRevIndexTable64</a>, <a class="el" href="arm__common__tables_8h.html#abb73376f7efda869394aab2acef4291c">ARMBITREVINDEXTABLE_128_TABLE_LENGTH</a>, <a class="el" href="arm__common__tables_8h.html#aa7dc18c3b4f8d76f5a29f7b182007934">ARMBITREVINDEXTABLE_256_TABLE_LENGTH</a>, <a class="el" href="arm__common__tables_8h.html#ab21231782baf177ef3edad11aeba5a4f">ARMBITREVINDEXTABLE_512_TABLE_LENGTH</a>, <a class="el" href="arm__common__tables_8h.html#a52289ebb691669410fbc40d1a8a1562a">ARMBITREVINDEXTABLE__16_TABLE_LENGTH</a>, <a class="el" href="arm__common__tables_8h.html#a6e12fc7073f15899078a1b2d8f4afb4c">ARMBITREVINDEXTABLE__32_TABLE_LENGTH</a>, <a class="el" href="arm__common__tables_8h.html#a73e1987baf5282c699168bccf635930e">ARMBITREVINDEXTABLE__64_TABLE_LENGTH</a>, <a class="el" href="structarm__cfft__instance__f32.html#a3ba329ed153d182746376208e773d648">arm_cfft_instance_f32::bitRevLength</a>, <a class="el" href="structarm__cfft__instance__f32.html#acd8f9e9540e3dd348212726e5d6aaa95">arm_cfft_instance_f32::fftLen</a>, <a class="el" href="structarm__rfft__fast__instance__f32.html#aef06ab665041ec36f5b25d464f0cab14">arm_rfft_fast_instance_f32::fftLenRFFT</a>, <a class="el" href="structarm__cfft__instance__f32.html#a21ceaf59a1bb8440af57c28d2dd9bbab">arm_cfft_instance_f32::pBitRevTable</a>, <a class="el" href="structarm__cfft__instance__f32.html#a59cc6f753f1498716e1444ac054c06de">arm_cfft_instance_f32::pTwiddle</a>, <a class="el" href="structarm__rfft__fast__instance__f32.html#a9f30b04f163fabc1b24421d3c323d5fc">arm_rfft_fast_instance_f32::pTwiddleRFFT</a>, <a class="el" href="structarm__rfft__fast__instance__f32.html#a37419ababdfb3151b1891ae6bcd21012">arm_rfft_fast_instance_f32::Sint</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga27c056eb130a4333d1cc5dd43ec738b1">twiddleCoef_1024</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga948433536dafaac1381decfccf4e2d9c">twiddleCoef_128</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gae75e243ec61706427314270f222e0c8e">twiddleCoef_16</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga23e7f30421a7905b21c2015429779633">twiddleCoef_2048</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gafe813758a03a798e972359a092315be4">twiddleCoef_256</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga78a72c85d88185de98050c930cfc76e3">twiddleCoef_32</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#gad8830f0c068ab2cc19f2f87d220fa148">twiddleCoef_512</a>, <a class="el" href="group___c_f_f_t___c_i_f_f_t.html#ga4f3c6d98c7e66393b4ef3ac63746e43d">twiddleCoef_64</a>, <a class="el" href="arm__common__tables_8c.html#aa7d8d3aa9898d557385748a13c959a4c">twiddleCoef_rfft_1024</a>, <a class="el" href="arm__common__tables_8c.html#af089dd2fe1a543d40a3325982bf45e7c">twiddleCoef_rfft_128</a>, <a class="el" href="arm__common__tables_8c.html#a749a5995ebd433a163f7adc474dabcaa">twiddleCoef_rfft_2048</a>, <a class="el" href="arm__common__tables_8c.html#a5c5c161dd469d8e6806664956dae31f9">twiddleCoef_rfft_256</a>, <a class="el" href="arm__common__tables_8c.html#a5992afe8574289cd71921651b80bd57d">twiddleCoef_rfft_32</a>, <a class="el" href="arm__common__tables_8c.html#a8013d68dd2476c86b77173bb98b87b29">twiddleCoef_rfft_4096</a>, <a class="el" href="arm__common__tables_8c.html#a94bd2fc98798f87003fef5cd0c04d1f5">twiddleCoef_rfft_512</a>, and <a class="el" href="arm__common__tables_8c.html#a2759d8789e1e6ae2ba7fb8d7f5e9c2ab">twiddleCoef_rfft_64</a>.</p>

</div>
</div>
<a class="anchor" id="ga10717ee326bf50832ef1c25b85a23068"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_rfft_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__rfft__instance__f32.html">arm_rfft_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structarm__cfft__radix4__instance__f32.html">arm_cfft_radix4_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S_CFFT</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>fftLenReal</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>ifftFlagR</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>bitReverseFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000014">Deprecated:</a></b></dt><dd>Do not use this function. It has been superceded by <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32</a> and will be removed in the future. </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the floating-point RFFT/RIFFT structure. </td></tr>
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S_CFFT</td><td>points to an instance of the floating-point CFFT/CIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fftLenReal</td><td>length of the FFT. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlagR</td><td>flag that selects forward (ifftFlagR=0) or inverse (ifftFlagR=1) transform. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">bitReverseFlag</td><td>flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLenReal</code> is not a supported value.</dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>fftLenReal</code> Specifies length of RFFT/RIFFT Process. Supported FFT Lengths are 128, 512, 2048. </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>ifftFlagR</code> controls whether a forward or inverse transform is computed. Set(=1) ifftFlagR to calculate RIFFT, otherwise RFFT is calculated. </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>bitReverseFlag</code> controls whether output is in normal order or bit reversed order. Set(=1) bitReverseFlag for output to be in normal order otherwise output is in bit reversed order. </dd></dl>
<dl class="section user"><dt></dt><dd>This function also initializes Twiddle factor table. </dd></dl>

<p>References <a class="el" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a">arm_cfft_radix4_init_f32()</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="structarm__rfft__instance__f32.html#ac342f3248157cbbd2f04a3c8ec9fc9eb">arm_rfft_instance_f32::bitReverseFlagR</a>, <a class="el" href="structarm__rfft__instance__f32.html#a075076e07ebb8521d8e3b49a31db6c57">arm_rfft_instance_f32::fftLenBy2</a>, <a class="el" href="structarm__rfft__instance__f32.html#a4219d4669699e4efdcb150ed7a0d9a57">arm_rfft_instance_f32::fftLenReal</a>, <a class="el" href="structarm__rfft__instance__f32.html#a5ee6d10a934ab4b666e0bb286c3d633f">arm_rfft_instance_f32::ifftFlagR</a>, <a class="el" href="structarm__rfft__instance__f32.html#a9f47ba9f50c81e4445ae3827b981bc05">arm_rfft_instance_f32::pCfft</a>, <a class="el" href="structarm__rfft__instance__f32.html#a534cc7e6e9b3e3dd022fad611c762142">arm_rfft_instance_f32::pTwiddleAReal</a>, <a class="el" href="structarm__rfft__instance__f32.html#a23543ecfd027fea2477fe1eea23c3c4d">arm_rfft_instance_f32::pTwiddleBReal</a>, <a class="el" href="group___real_f_f_t.html#ga8b1ad947c470596674fa3364e16045c6">realCoefA</a>, <a class="el" href="group___real_f_f_t.html#gac52f98b52a1f03bfac8b57a67ba07397">realCoefB</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, and <a class="el" href="structarm__rfft__instance__f32.html#aede85350fb5ae6baa1b3e8bfa15b18d6">arm_rfft_instance_f32::twidCoefRModifier</a>.</p>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06">arm_dct4_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ga053450cc600a55410ba5b5605e96245d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_rfft_init_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__rfft__instance__q15.html">arm_rfft_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>fftLenReal</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>ifftFlagR</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>bitReverseFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q15 RFFT/RIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fftLenReal</td><td>length of the FFT. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlagR</td><td>flag that selects forward (ifftFlagR=0) or inverse (ifftFlagR=1) transform. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">bitReverseFlag</td><td>flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLenReal</code> is not a supported value.</dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>fftLenReal</code> Specifies length of RFFT/RIFFT Process. Supported FFT Lengths are 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192. </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>ifftFlagR</code> controls whether a forward or inverse transform is computed. Set(=1) ifftFlagR to calculate RIFFT, otherwise RFFT is calculated. </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>bitReverseFlag</code> controls whether output is in normal order or bit reversed order. Set(=1) bitReverseFlag for output to be in normal order otherwise output is in bit reversed order. </dd></dl>
<dl class="section user"><dt></dt><dd>This function also initializes Twiddle factor table. </dd></dl>

<p>References <a class="el" href="arm__const__structs_8c.html#ad343fb2e4cba826f092f9d72c4adc831">arm_cfft_sR_q15_len1024</a>, <a class="el" href="arm__const__structs_8c.html#a736a97efd37c6386dab8db730904f69b">arm_cfft_sR_q15_len128</a>, <a class="el" href="arm__const__structs_8c.html#a7ed661717c58b18f3e557daa72f2b91b">arm_cfft_sR_q15_len16</a>, <a class="el" href="arm__const__structs_8c.html#a92c94dc79c66ec66c95f793aedb964b9">arm_cfft_sR_q15_len2048</a>, <a class="el" href="arm__const__structs_8c.html#ad80be0db1ea40c66b079404c48d2dcf4">arm_cfft_sR_q15_len256</a>, <a class="el" href="arm__const__structs_8c.html#a8d5426a822a6017235b5e10119606a90">arm_cfft_sR_q15_len32</a>, <a class="el" href="arm__const__structs_8c.html#ab57c118edaa3260f7f16686152845b18">arm_cfft_sR_q15_len4096</a>, <a class="el" href="arm__const__structs_8c.html#a273b91ec86bb2bd8ac14e69252d487fb">arm_cfft_sR_q15_len512</a>, <a class="el" href="arm__const__structs_8c.html#a95c216e7dcfd59a8d40ef55ac223a749">arm_cfft_sR_q15_len64</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="structarm__rfft__instance__q15.html#a4c65cd40e0098ec2f5c0dc31488b9bc6">arm_rfft_instance_q15::bitReverseFlagR</a>, <a class="el" href="structarm__rfft__instance__q15.html#aac5cf9e825917cbb14f439e56bb86ab3">arm_rfft_instance_q15::fftLenReal</a>, <a class="el" href="structarm__rfft__instance__q15.html#a8051ffe268c147e431e1bea7bb4c4258">arm_rfft_instance_q15::ifftFlagR</a>, <a class="el" href="structarm__rfft__instance__q15.html#a4329c15b056444746d37ff082a24d31a">arm_rfft_instance_q15::pCfft</a>, <a class="el" href="structarm__rfft__instance__q15.html#affbf2de522ac029432d98e8373c0ec53">arm_rfft_instance_q15::pTwiddleAReal</a>, <a class="el" href="structarm__rfft__instance__q15.html#a937d815022adc557b435ba8c6cd58b0d">arm_rfft_instance_q15::pTwiddleBReal</a>, <a class="el" href="group___real_f_f_t.html#ga11e84d0ee257a547f749b37dd0078d36">realCoefAQ15</a>, <a class="el" href="group___real_f_f_t.html#gac871666f018b70938b2b98017628cb97">realCoefBQ15</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, and <a class="el" href="structarm__rfft__instance__q15.html#afd444d05858c5f419980e94e8240d5c3">arm_rfft_instance_q15::twidCoefRModifier</a>.</p>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="ga5abde938abbe72e95c5bab080eb33c45"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> arm_rfft_init_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__rfft__instance__q31.html">arm_rfft_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>fftLenReal</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>ifftFlagR</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>bitReverseFlag</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in,out]</td><td class="paramname">*S</td><td>points to an instance of the Q31 RFFT/RIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fftLenReal</td><td>length of the FFT. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">ifftFlagR</td><td>flag that selects forward (ifftFlagR=0) or inverse (ifftFlagR=1) transform. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">bitReverseFlag</td><td>flag that enables (bitReverseFlag=1) or disables (bitReverseFlag=0) bit reversal of output. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The function returns ARM_MATH_SUCCESS if initialization is successful or ARM_MATH_ARGUMENT_ERROR if <code>fftLenReal</code> is not a supported value.</dd></dl>
<dl class="section user"><dt>Description: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>fftLenReal</code> Specifies length of RFFT/RIFFT Process. Supported FFT Lengths are 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192. </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>ifftFlagR</code> controls whether a forward or inverse transform is computed. Set(=1) ifftFlagR to calculate RIFFT, otherwise RFFT is calculated. </dd></dl>
<dl class="section user"><dt></dt><dd>The parameter <code>bitReverseFlag</code> controls whether output is in normal order or bit reversed order. Set(=1) bitReverseFlag for output to be in normal order otherwise output is in bit reversed order. </dd></dl>
<dl class="section user"><dt>7</dt><dd>This function also initializes Twiddle factor table. </dd></dl>

<p>References <a class="el" href="arm__const__structs_8c.html#ada9813a027999f3cff066c9f7b5df51b">arm_cfft_sR_q31_len1024</a>, <a class="el" href="arm__const__structs_8c.html#a9a2fcdb54300f75ef1fafe02954e9a61">arm_cfft_sR_q31_len128</a>, <a class="el" href="arm__const__structs_8c.html#a1336431c4d2a88d32c42308cfe2defa1">arm_cfft_sR_q31_len16</a>, <a class="el" href="arm__const__structs_8c.html#a420622d75b277070784083ddd44b95fb">arm_cfft_sR_q31_len2048</a>, <a class="el" href="arm__const__structs_8c.html#a3f2de67938bd228918e40f60f18dd6b5">arm_cfft_sR_q31_len256</a>, <a class="el" href="arm__const__structs_8c.html#a4c083c013ef17920cf8f28dc6f139a39">arm_cfft_sR_q31_len32</a>, <a class="el" href="arm__const__structs_8c.html#abfc9595f40a1c7aaba85e1328d824b1c">arm_cfft_sR_q31_len4096</a>, <a class="el" href="arm__const__structs_8c.html#aa337272cf78aaf6075e7e19d0a097d6f">arm_cfft_sR_q31_len512</a>, <a class="el" href="arm__const__structs_8c.html#ad11668a5662334e0bc6a2811c9cb1047">arm_cfft_sR_q31_len64</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a74897e18d4b8f62b12a7d8a01dd2bb35">ARM_MATH_ARGUMENT_ERROR</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="structarm__rfft__instance__q31.html#a3cb90cdc928a88b0203917dcb3dc1b71">arm_rfft_instance_q31::bitReverseFlagR</a>, <a class="el" href="structarm__rfft__instance__q31.html#af777b0cadd5abaf064323692c2e6693b">arm_rfft_instance_q31::fftLenReal</a>, <a class="el" href="structarm__rfft__instance__q31.html#af5c2615e6cde15524df38fa57ea32d94">arm_rfft_instance_q31::ifftFlagR</a>, <a class="el" href="structarm__rfft__instance__q31.html#a8fe10d425b59e096c23aa4bb5caa1974">arm_rfft_instance_q31::pCfft</a>, <a class="el" href="structarm__rfft__instance__q31.html#a2a0c944e66bab92fcbe19d1c29153250">arm_rfft_instance_q31::pTwiddleAReal</a>, <a class="el" href="structarm__rfft__instance__q31.html#ae5070be4c2e0327e618f5e1f4c5b9d80">arm_rfft_instance_q31::pTwiddleBReal</a>, <a class="el" href="group___real_f_f_t.html#gaf1592a6cf0504675205074a43c3728a2">realCoefAQ31</a>, <a class="el" href="group___real_f_f_t.html#ga1eb5745728a61c3715755f5d69a4a960">realCoefBQ31</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, and <a class="el" href="structarm__rfft__instance__q31.html#a6fc90252b579f7c29e01bd279334fc43">arm_rfft_instance_q31::twidCoefRModifier</a>.</p>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a">arm_dct4_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="ga00e615f5db21736ad5b27fb6146f3fc5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_rfft_q15 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__rfft__instance__q15.html">arm_rfft_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 RFFT/RIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the input buffer. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the output buffer. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<dl class="section user"><dt>Input an output formats: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>Internally input is downscaled by 2 for every stage to avoid saturations inside CFFT/CIFFT process. Hence the output format is different for different RFFT sizes. The input and output formats for different RFFT sizes and number of bits to upscale are mentioned in the tables below for RFFT and RIFFT: </dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="RFFTQ15.gif" alt="RFFTQ15.gif"/>
<div class="caption">
Input and Output Formats for Q15 RFFT</div></div>
 </dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="RIFFTQ15.gif" alt="RIFFTQ15.gif"/>
<div class="caption">
Input and Output Formats for Q15 RIFFT</div></div>
 </dd></dl>

<p>References <a class="el" href="group___complex_f_f_t.html#ga68cdacd2267a2967955e40e6b7ec1229">arm_cfft_q15()</a>, <a class="el" href="arm__rfft__q15_8c.html#a7c2a21793586f9a69c42140665550e09">arm_split_rfft_q15()</a>, <a class="el" href="arm__rfft__q15_8c.html#aa72a531dd15a53570dddaf01b62158f4">arm_split_rifft_q15()</a>, <a class="el" href="structarm__rfft__instance__q15.html#a4c65cd40e0098ec2f5c0dc31488b9bc6">arm_rfft_instance_q15::bitReverseFlagR</a>, <a class="el" href="structarm__rfft__instance__q15.html#aac5cf9e825917cbb14f439e56bb86ab3">arm_rfft_instance_q15::fftLenReal</a>, <a class="el" href="structarm__rfft__instance__q15.html#a8051ffe268c147e431e1bea7bb4c4258">arm_rfft_instance_q15::ifftFlagR</a>, <a class="el" href="structarm__rfft__instance__q15.html#a4329c15b056444746d37ff082a24d31a">arm_rfft_instance_q15::pCfft</a>, <a class="el" href="structarm__rfft__instance__q15.html#affbf2de522ac029432d98e8373c0ec53">arm_rfft_instance_q15::pTwiddleAReal</a>, <a class="el" href="structarm__rfft__instance__q15.html#a937d815022adc557b435ba8c6cd58b0d">arm_rfft_instance_q15::pTwiddleBReal</a>, and <a class="el" href="structarm__rfft__instance__q15.html#afd444d05858c5f419980e94e8240d5c3">arm_rfft_instance_q15::twidCoefRModifier</a>.</p>

</div>
</div>
<a class="anchor" id="gabaeab5646aeea9844e6d42ca8c73fe3a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_rfft_q31 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__rfft__instance__q31.html">arm_rfft_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q31 RFFT/RIFFT structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the input buffer. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the output buffer. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<dl class="section user"><dt>Input an output formats: </dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>Internally input is downscaled by 2 for every stage to avoid saturations inside CFFT/CIFFT process. Hence the output format is different for different RFFT sizes. The input and output formats for different RFFT sizes and number of bits to upscale are mentioned in the tables below for RFFT and RIFFT: </dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="RFFTQ31.gif" alt="RFFTQ31.gif"/>
<div class="caption">
Input and Output Formats for Q31 RFFT</div></div>
</dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="RIFFTQ31.gif" alt="RIFFTQ31.gif"/>
<div class="caption">
Input and Output Formats for Q31 RIFFT</div></div>
 </dd></dl>

<p>References <a class="el" href="group___complex_f_f_t.html#ga5a0008bd997ab6e2e299ef2fb272fb4b">arm_cfft_q31()</a>, <a class="el" href="arm__rfft__q31_8c.html#a520e1c358d44fcd2724cb19d46eb5dfa">arm_split_rfft_q31()</a>, <a class="el" href="arm__rfft__q31_8c.html#acc62dd39a59091c4d6a80d4e55adeb13">arm_split_rifft_q31()</a>, <a class="el" href="structarm__rfft__instance__q31.html#a3cb90cdc928a88b0203917dcb3dc1b71">arm_rfft_instance_q31::bitReverseFlagR</a>, <a class="el" href="structarm__rfft__instance__q31.html#af777b0cadd5abaf064323692c2e6693b">arm_rfft_instance_q31::fftLenReal</a>, <a class="el" href="structarm__rfft__instance__q31.html#af5c2615e6cde15524df38fa57ea32d94">arm_rfft_instance_q31::ifftFlagR</a>, <a class="el" href="structarm__rfft__instance__q31.html#a8fe10d425b59e096c23aa4bb5caa1974">arm_rfft_instance_q31::pCfft</a>, <a class="el" href="structarm__rfft__instance__q31.html#a2a0c944e66bab92fcbe19d1c29153250">arm_rfft_instance_q31::pTwiddleAReal</a>, <a class="el" href="structarm__rfft__instance__q31.html#ae5070be4c2e0327e618f5e1f4c5b9d80">arm_rfft_instance_q31::pTwiddleBReal</a>, and <a class="el" href="structarm__rfft__instance__q31.html#a6fc90252b579f7c29e01bd279334fc43">arm_rfft_instance_q31::twidCoefRModifier</a>.</p>

<p>Referenced by <a class="el" href="group___d_c_t4___i_d_c_t4.html#gad04d0baab6ed081d8e8afe02538eb80b">arm_dct4_q31()</a>.</p>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="ga8b1ad947c470596674fa3364e16045c6"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> realCoefA[8192]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Generation of realCoefA array: </dd></dl>
<dl class="section user"><dt></dt><dd>n = 4096 <pre>for (i = 0; i &lt; n; i++)    
 {    
   pATable[2 * i] = 0.5 * (1.0 - sin (2 * PI / (double) (2 * n) * (double) i));    
   pATable[2 * i + 1] = 0.5 * (-1.0 * cos (2 * PI / (double) (2 * n) * (double) i));    
 } </pre> </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="ga11e84d0ee257a547f749b37dd0078d36"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a> realCoefAQ15[8192]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Generation fixed-point realCoefAQ15 array in Q15 format: </dd></dl>
<dl class="section user"><dt></dt><dd>n = 4096 <pre>for (i = 0; i &lt; n; i++)    
 {    
   pATable[2 * i] = 0.5 * (1.0 - sin (2 * PI / (double) (2 * n) * (double) i));    
   pATable[2 * i + 1] = 0.5 * (-1.0 * cos (2 * PI / (double) (2 * n) * (double) i));    
 } </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>Convert to fixed point Q15 format round(pATable[i] * pow(2, 15)) </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="gaf1592a6cf0504675205074a43c3728a2"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> realCoefAQ31[8192]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Generation fixed-point realCoefAQ31 array in Q31 format: </dd></dl>
<dl class="section user"><dt></dt><dd>n = 4096 <pre>for (i = 0; i &lt; n; i++)    
{    
   pATable[2 * i] = 0.5 * (1.0 - sin (2 * PI / (double) (2 * n) * (double) i));    
   pATable[2 * i + 1] = 0.5 * (-1.0 * cos (2 * PI / (double) (2 * n) * (double) i));    
}</pre> </dd></dl>
<dl class="section user"><dt></dt><dd>Convert to fixed point Q31 format round(pATable[i] * pow(2, 31)) </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>.</p>

</div>
</div>
<a class="anchor" id="gac52f98b52a1f03bfac8b57a67ba07397"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> realCoefB[8192]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Generation of realCoefB array: </dd></dl>
<dl class="section user"><dt></dt><dd>n = 4096 <pre>for (i = 0; i &lt; n; i++)    
{    
   pBTable[2 * i] = 0.5 * (1.0 + sin (2 * PI / (double) (2 * n) * (double) i));    
   pBTable[2 * i + 1] = 0.5 * (1.0 * cos (2 * PI / (double) (2 * n) * (double) i));    
 } </pre> </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>.</p>

</div>
</div>
<a class="anchor" id="gac871666f018b70938b2b98017628cb97"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> <a class="el" href="arm__math_8h.html#a280a402ab28c399fcc4168f2ed631acb">ALIGN4</a> realCoefBQ15[8192]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Generation of real_CoefB array: </dd></dl>
<dl class="section user"><dt></dt><dd>n = 4096 <pre>for (i = 0; i &lt; n; i++)    
 {    
   pBTable[2 * i] = 0.5 * (1.0 + sin (2 * PI / (double) (2 * n) * (double) i));    
   pBTable[2 * i + 1] = 0.5 * (1.0 * cos (2 * PI / (double) (2 * n) * (double) i));    
 } </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>Convert to fixed point Q15 format round(pBTable[i] * pow(2, 15)) </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>.</p>

</div>
</div>
<a class="anchor" id="ga1eb5745728a61c3715755f5d69a4a960"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> realCoefBQ31[8192]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section user"><dt></dt><dd>Generation of realCoefBQ31 array: </dd></dl>
<dl class="section user"><dt></dt><dd>n = 4096 <pre>for (i = 0; i &lt; n; i++)    
{    
   pBTable[2 * i] = 0.5 * (1.0 + sin (2 * PI / (double) (2 * n) * (double) i));    
   pBTable[2 * i + 1] = 0.5 * (1.0 * cos (2 * PI / (double) (2 * n) * (double) i));    
} </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>Convert to fixed point Q31 format round(pBTable[i] * pow(2, 31)) </dd></dl>

<p>Referenced by <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
