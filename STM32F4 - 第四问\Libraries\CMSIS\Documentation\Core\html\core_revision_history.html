<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Revision History of CMSIS-CORE</title>
<title>CMSIS-CORE: Revision History of CMSIS-CORE</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('core_revision_history.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Revision History of CMSIS-CORE </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><table  class="cmtable" summary="Core Exception Name">
<tr>
<th>Version </th><th>Description  </th></tr>
<tr>
<td>V4.10 </td><td>Corrected: MISRA-C:2004 violations. <br/>
 Corrected: intrinsic functions <a class="el" href="group__intrinsic___c_p_u__gr.html#gacb2a8ca6eae1ba4b31161578b720c199">__DSB</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#gab1c9b393641dc2d397b3408fdbe72b96">__DMB</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#ga93c09b4709394d81977300d5f84950e5">__ISB</a>. <br/>
 Corrected: register definitions for ITCMCR register. <br/>
 Corrected: register definitions for <a class="el" href="union_c_o_n_t_r_o_l___type.html">CONTROL_Type</a> register. <br/>
 Added: functions <a class="el" href="group__fpu__functions__m7.html#ga6bcad99ce80a0e7e4ddc6f2379081756">SCB_GetFPUType</a>, <a class="el" href="group___dcache__functions__m7.html#ga503ef7ef58c0773defd15a82f6336c09">SCB_InvalidateDCache_by_Addr</a> to core_cm7.h. <br/>
 Added: register definitions for <a class="el" href="union_a_p_s_r___type.html">APSR_Type</a>, <a class="el" href="union_i_p_s_r___type.html">IPSR_Type</a>, <a class="el" href="unionx_p_s_r___type.html">xPSR_Type</a> register. <br/>
 Added: <a class="el" href="group___core___register__gr.html#ga62fa63d39cf22df348857d5f44ab64d9">__set_BASEPRI_MAX</a> function to core_cmFunc.h. <br/>
 Added: intrinsic functions <a class="el" href="group__intrinsic___c_p_u__gr.html#gad6f9f297f6b91a995ee199fbc796b863">__RBIT</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#ga90884c591ac5d73d6069334eba9d6c02">__CLZ</a> for Cortex-M0/CortexM0+. <br/>
   </td></tr>
<tr>
<td>V4.00 </td><td>Added: Cortex-M7 support.<br/>
 Added: intrinsic functions for <a class="el" href="group__intrinsic___c_p_u__gr.html#gac09134f1bf9c49db07282001afcc9380">__RRX</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#ga9464d75db32846aa8295c3c3adfacb41">__LDRBT</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#gaa762b8bc5634ce38cb14d62a6b2aee32">__LDRHT</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#ga616504f5da979ba8a073d428d6e8d5c7">__LDRT</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#gad41aa59c92c0a165b7f98428d3320cd5">__STRBT</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#ga2b5d93b8e461755b1072a03df3f1722e">__STRHT</a>, and <a class="el" href="group__intrinsic___c_p_u__gr.html#ga625bc4ac0b1d50de9bcd13d9f050030e">__STRT</a> <br/>
   </td></tr>
<tr>
<td>V3.40 </td><td>Corrected: C++ include guard settings.<br/>
   </td></tr>
<tr>
<td>V3.30 </td><td>Added: COSMIC tool chain support.<br/>
 Corrected: GCC __SMLALDX instruction intrinsic for Cortex-M4.<br/>
 Corrected: GCC __SMLALD instruction intrinsic for Cortex-M4.<br/>
 Corrected: GCC/CLang warnings.<br/>
   </td></tr>
<tr>
<td>V3.20 </td><td>Added: <a class="el" href="group__intrinsic___c_p_u__gr.html#ga92f5621626711931da71eaa8bf301af7">__BKPT</a> instruction intrinsic.<br/>
 Added: <a class="el" href="group__intrinsic___s_i_m_d__gr.html#gaea60757232f740ec6b09980eebb614ff">__SMMLA</a> instruction intrinsic for Cortex-M4.<br/>
 Corrected: <a class="el" href="group___i_t_m___debug__gr.html#gaaa7c716331f74d644bf6bf25cd3392d1">ITM_SendChar</a>.<br/>
 Corrected: <a class="el" href="group___core___register__gr.html#ga0f98dfbd252b89d12564472dbeba9c27">__enable_irq</a>, <a class="el" href="group___core___register__gr.html#gaeb8e5f7564a8ea23678fe3c987b04013">__disable_irq</a> and inline assembly for GCC Compiler.<br/>
 Corrected: <a class="el" href="group___n_v_i_c__gr.html#gab18fb9f6c5f4c70fdd73047f0f7c8395">NVIC_GetPriority</a> and VTOR_TBLOFF for Cortex-M0/M0+, SC000. Corrected: rework of in-line assembly functions to remove potential compiler warnings.<br/>
   </td></tr>
<tr>
<td>V3.01 </td><td>Added support for Cortex-M0+ processor. <br/>
   </td></tr>
<tr>
<td>V3.00 </td><td>Added support for GNU GCC ARM Embedded Compiler. <br/>
 Added function <a class="el" href="group__intrinsic___c_p_u__gr.html#gaf66beb577bb9d90424c3d1d7f684c024">__ROR</a>.<br/>
 Added <a class="el" href="_reg_map_pg.html">Register Mapping</a> for TPIU, DWT. <br/>
 Added support for <a class="el" href="device_h_pg.html#core_config_sect">SC000 and SC300 processors</a>.<br/>
 Corrected <a class="el" href="group___i_t_m___debug__gr.html#gaaa7c716331f74d644bf6bf25cd3392d1">ITM_SendChar</a> function. <br/>
 Corrected the functions <a class="el" href="group__intrinsic___c_p_u__gr.html#gaab6482d1f59f59e2b6b7efc1af391c99">__STREXB</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#ga0a354bdf71caa52f081a4a54e84c8d2a">__STREXH</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#ga335deaaa7991490e1450cb7d1e4c5197">__STREXW</a> for the GNU GCC compiler section. <br/>
 Documentation restructured.   </td></tr>
<tr>
<td>V2.10 </td><td>Updated documentation.<br/>
 Updated CMSIS core include files.<br/>
 Changed CMSIS/Device folder structure.<br/>
 Added support for Cortex-M0, Cortex-M4 w/o FPU to CMSIS DSP library.<br/>
 Reworked CMSIS DSP library examples.   </td></tr>
<tr>
<td>V2.00 </td><td>Added support for Cortex-M4 processor.  </td></tr>
<tr>
<td>V1.30 </td><td>Reworked Startup Concept.<br/>
 Added additional Debug Functionality.<br/>
 Changed folder structure.<br/>
 Added doxygen comments.<br/>
 Added definitions for bit.   </td></tr>
<tr>
<td>V1.01 </td><td>Added support for Cortex-M0 processor.  </td></tr>
<tr>
<td>V1.01 </td><td>Added intrinsic functions for <a class="el" href="group__intrinsic___c_p_u__gr.html#ga9e3ac13d8dcf4331176b624cf6234a7e">__LDREXB</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#ga9feffc093d6f68b120d592a7a0d45a15">__LDREXH</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#gabd78840a0f2464905b7cec791ebc6a4c">__LDREXW</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#gaab6482d1f59f59e2b6b7efc1af391c99">__STREXB</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#ga0a354bdf71caa52f081a4a54e84c8d2a">__STREXH</a>, <a class="el" href="group__intrinsic___c_p_u__gr.html#ga335deaaa7991490e1450cb7d1e4c5197">__STREXW</a>, and <a class="el" href="group__intrinsic___c_p_u__gr.html#ga354c5ac8870cc3dfb823367af9c4b412">__CLREX</a>  </td></tr>
<tr>
<td>V1.00 </td><td>Initial Release for Cortex-M3 processor.  </td></tr>
</table>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
