var searchData=
[
  ['b_5ff32',['B_f32',['../arm__matrix__example__f32_8c.html#a974d5f0aace6a99e29ca767907fb3b9f',1,'arm_matrix_example_f32.c']]],
  ['bilinear_20interpolation',['Bilinear Interpolation',['../group___bilinear_interpolate.html',1,'']]],
  ['biquad_20cascade_20iir_20filters_20using_20direct_20form_20i_20structure',['Biquad Cascade IIR Filters Using Direct Form I Structure',['../group___biquad_cascade_d_f1.html',1,'']]],
  ['biquad_20cascade_20iir_20filters_20using_20a_20direct_20form_20ii_20transposed_20structure',['Biquad Cascade IIR Filters Using a Direct Form II Transposed Structure',['../group___biquad_cascade_d_f2_t.html',1,'']]],
  ['biquadstateband1q31',['biquadStateBand1Q31',['../arm__graphic__equalizer__example__q31_8c.html#a8e7062fa3f8b5ed9849566d16270f4ec',1,'arm_graphic_equalizer_example_q31.c']]],
  ['biquadstateband2q31',['biquadStateBand2Q31',['../arm__graphic__equalizer__example__q31_8c.html#a4a82090b15ebd0a45048d94f16131782',1,'arm_graphic_equalizer_example_q31.c']]],
  ['biquadstateband3q31',['biquadStateBand3Q31',['../arm__graphic__equalizer__example__q31_8c.html#ad487a54340631b764952fccf599adc8a',1,'arm_graphic_equalizer_example_q31.c']]],
  ['biquadstateband4q31',['biquadStateBand4Q31',['../arm__graphic__equalizer__example__q31_8c.html#a122ed5f6d8665139fbe9424a073b3474',1,'arm_graphic_equalizer_example_q31.c']]],
  ['biquadstateband5q31',['biquadStateBand5Q31',['../arm__graphic__equalizer__example__q31_8c.html#af88658a8c5f87eeea26da30305921b59',1,'arm_graphic_equalizer_example_q31.c']]],
  ['bitreverseflag',['bitReverseFlag',['../structarm__cfft__radix2__instance__q15.html#af8300c1f60caa21e6b44b9240ab5af19',1,'arm_cfft_radix2_instance_q15::bitReverseFlag()'],['../structarm__cfft__radix4__instance__q15.html#a101e3f7b0bd6b5b14cd5214f23df4133',1,'arm_cfft_radix4_instance_q15::bitReverseFlag()'],['../structarm__cfft__radix2__instance__q31.html#a6239b8d268285334e88c008c07d68616',1,'arm_cfft_radix2_instance_q31::bitReverseFlag()'],['../structarm__cfft__radix4__instance__q31.html#a5a7c4f4c7b3fb655cbb2bc11ef160a2a',1,'arm_cfft_radix4_instance_q31::bitReverseFlag()'],['../structarm__cfft__radix2__instance__f32.html#af713b4ac5256a19bc965c89fe3005fa3',1,'arm_cfft_radix2_instance_f32::bitReverseFlag()'],['../structarm__cfft__radix4__instance__f32.html#ac10927a1620195a88649ce63dab66120',1,'arm_cfft_radix4_instance_f32::bitReverseFlag()']]],
  ['bitreverseflagr',['bitReverseFlagR',['../structarm__rfft__instance__q15.html#a4c65cd40e0098ec2f5c0dc31488b9bc6',1,'arm_rfft_instance_q15::bitReverseFlagR()'],['../structarm__rfft__instance__q31.html#a3cb90cdc928a88b0203917dcb3dc1b71',1,'arm_rfft_instance_q31::bitReverseFlagR()'],['../structarm__rfft__instance__f32.html#ac342f3248157cbbd2f04a3c8ec9fc9eb',1,'arm_rfft_instance_f32::bitReverseFlagR()']]],
  ['bitrevfactor',['bitRevFactor',['../structarm__cfft__radix2__instance__q15.html#a8722720c542cabd41df83fe88ef4f4cb',1,'arm_cfft_radix2_instance_q15::bitRevFactor()'],['../structarm__cfft__radix4__instance__q15.html#a6b010e5f02d1130c621e3d2e26b95df1',1,'arm_cfft_radix4_instance_q15::bitRevFactor()'],['../structarm__cfft__radix2__instance__q31.html#a9d17a87263953fe3559a007512c9f3a4',1,'arm_cfft_radix2_instance_q31::bitRevFactor()'],['../structarm__cfft__radix4__instance__q31.html#a94d2fead4efa4d5eaae142bbe30b0e15',1,'arm_cfft_radix4_instance_q31::bitRevFactor()'],['../structarm__cfft__radix2__instance__f32.html#ac1688dafa5177f6b1505abbfd0cf8b21',1,'arm_cfft_radix2_instance_f32::bitRevFactor()'],['../structarm__cfft__radix4__instance__f32.html#acc8cb18a8b901b8321ab9d86491e41a3',1,'arm_cfft_radix4_instance_f32::bitRevFactor()']]],
  ['bitrevlength',['bitRevLength',['../structarm__cfft__instance__q15.html#a738907cf34bdbbaf724414ac2decbc3c',1,'arm_cfft_instance_q15::bitRevLength()'],['../structarm__cfft__instance__q31.html#a2250fa6b8fe73292c5418c50c0549f87',1,'arm_cfft_instance_q31::bitRevLength()'],['../structarm__cfft__instance__f32.html#a3ba329ed153d182746376208e773d648',1,'arm_cfft_instance_f32::bitRevLength()']]],
  ['bk',['Bk',['../_a_r_m_2arm__convolution__example__f32_8c.html#a88a0167516ae7ed66203fd60e6ddeea3',1,'Bk():&#160;arm_convolution_example_f32.c'],['../_g_c_c_2arm__convolution__example__f32_8c.html#a88a0167516ae7ed66203fd60e6ddeea3',1,'Bk():&#160;arm_convolution_example_f32.c']]],
  ['block_5fsize',['BLOCK_SIZE',['../arm__fir__example__f32_8c.html#ad51ded0bbd705f02f73fc60c0b721ced',1,'arm_fir_example_f32.c']]],
  ['blocksize',['blockSize',['../arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0',1,'blockSize():&#160;arm_fir_example_f32.c'],['../arm__sin__cos__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0',1,'blockSize():&#160;arm_sin_cos_example_f32.c'],['../arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0',1,'blockSize():&#160;arm_variance_example_f32.c'],['../arm__graphic__equalizer__example__q31_8c.html#afcf795f5a96fd55561abe69f56224630',1,'BLOCKSIZE():&#160;arm_graphic_equalizer_example_q31.c'],['../arm__signal__converge__example__f32_8c.html#afcf795f5a96fd55561abe69f56224630',1,'BLOCKSIZE():&#160;arm_signal_converge_example_f32.c']]],
  ['basic_20math_20functions',['Basic Math Functions',['../group__group_math.html',1,'']]]
];
