<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Interrupts and Exceptions (NVIC)</title>
<title>CMSIS-CORE: Interrupts and Exceptions (NVIC)</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___n_v_i_c__gr.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#enum-members">Enumerations</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Interrupts and Exceptions (NVIC)</div>  </div>
</div><!--header-->
<div class="contents">

<p>Describes programming of interrupts and exception functions.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:ga7e1129cd8a196f4284d41db3e82ad5c8"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a> { <br/>
&#160;&#160;<a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8ade177d9c70c89e084093024b932a4e30">NonMaskableInt_IRQn</a> = -14, 
<br/>
&#160;&#160;<a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8ab1a222a34a32f0ef5ac65e714efc1f85">HardFault_IRQn</a> = -13, 
<br/>
&#160;&#160;<a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a33ff1cf7098de65d61b6354fee6cd5aa">MemoryManagement_IRQn</a> = -12, 
<br/>
&#160;&#160;<a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a8693500eff174f16119e96234fee73af">BusFault_IRQn</a> = -11, 
<br/>
&#160;&#160;<a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a6895237c9443601ac832efa635dd8bbf">UsageFault_IRQn</a> = -10, 
<br/>
&#160;&#160;<a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a4ce820b3cc6cf3a796b41aadc0cf1237">SVCall_IRQn</a> = -5, 
<br/>
&#160;&#160;<a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a8e033fcef7aed98a31c60a7de206722c">DebugMonitor_IRQn</a> = -4, 
<br/>
&#160;&#160;<a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a03c3cc89984928816d81793fc7bce4a2">PendSV_IRQn</a> = -2, 
<br/>
&#160;&#160;<a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a6dbff8f8543325f3474cbae2446776e7">SysTick_IRQn</a> = -1, 
<br/>
&#160;&#160;<a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8aa62e040960b4beb6cba107e4703c12d2">WWDG_STM_IRQn</a> = 0, 
<br/>
&#160;&#160;<a class="el" href="group___n_v_i_c__gr.html#gga7e1129cd8a196f4284d41db3e82ad5c8a853e0f318108110e0527f29733d11f86">PVD_STM_IRQn</a> = 1
<br/>
 }</td></tr>
<tr class="memdesc:ga7e1129cd8a196f4284d41db3e82ad5c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Definition of IRQn numbers.  <a href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">More...</a><br/></td></tr>
<tr class="separator:ga7e1129cd8a196f4284d41db3e82ad5c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gad78f447e891789b4d8f2e5b21eeda354"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#gad78f447e891789b4d8f2e5b21eeda354">NVIC_SetPriorityGrouping</a> (uint32_t PriorityGroup)</td></tr>
<tr class="memdesc:gad78f447e891789b4d8f2e5b21eeda354"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set priority grouping [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#gad78f447e891789b4d8f2e5b21eeda354"></a><br/></td></tr>
<tr class="separator:gad78f447e891789b4d8f2e5b21eeda354"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa81b19849367d3cdb95ac108c500fa78"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#gaa81b19849367d3cdb95ac108c500fa78">NVIC_GetPriorityGrouping</a> (void)</td></tr>
<tr class="memdesc:gaa81b19849367d3cdb95ac108c500fa78"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read the priority grouping [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#gaa81b19849367d3cdb95ac108c500fa78"></a><br/></td></tr>
<tr class="separator:gaa81b19849367d3cdb95ac108c500fa78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga530ad9fda2ed1c8b70e439ecfe80591f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#ga530ad9fda2ed1c8b70e439ecfe80591f">NVIC_EnableIRQ</a> (<a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a> IRQn)</td></tr>
<tr class="memdesc:ga530ad9fda2ed1c8b70e439ecfe80591f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Enable an external interrupt.  <a href="#ga530ad9fda2ed1c8b70e439ecfe80591f"></a><br/></td></tr>
<tr class="separator:ga530ad9fda2ed1c8b70e439ecfe80591f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga736ba13a76eb37ef6e2c253be8b0331c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#ga736ba13a76eb37ef6e2c253be8b0331c">NVIC_DisableIRQ</a> (<a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a> IRQn)</td></tr>
<tr class="memdesc:ga736ba13a76eb37ef6e2c253be8b0331c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Disable an external interrupt.  <a href="#ga736ba13a76eb37ef6e2c253be8b0331c"></a><br/></td></tr>
<tr class="separator:ga736ba13a76eb37ef6e2c253be8b0331c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga95a8329a680b051ecf3ee8f516acc662"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#ga95a8329a680b051ecf3ee8f516acc662">NVIC_GetPendingIRQ</a> (<a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a> IRQn)</td></tr>
<tr class="memdesc:ga95a8329a680b051ecf3ee8f516acc662"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the pending interrupt.  <a href="#ga95a8329a680b051ecf3ee8f516acc662"></a><br/></td></tr>
<tr class="separator:ga95a8329a680b051ecf3ee8f516acc662"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3b885147ef9965ecede49614de8df9d2"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#ga3b885147ef9965ecede49614de8df9d2">NVIC_SetPendingIRQ</a> (<a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a> IRQn)</td></tr>
<tr class="memdesc:ga3b885147ef9965ecede49614de8df9d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set an interrupt to pending.  <a href="#ga3b885147ef9965ecede49614de8df9d2"></a><br/></td></tr>
<tr class="separator:ga3b885147ef9965ecede49614de8df9d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga382ad6bedd6eecfdabd1b94dd128a01a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#ga382ad6bedd6eecfdabd1b94dd128a01a">NVIC_ClearPendingIRQ</a> (<a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a> IRQn)</td></tr>
<tr class="memdesc:ga382ad6bedd6eecfdabd1b94dd128a01a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clear an interrupt from pending.  <a href="#ga382ad6bedd6eecfdabd1b94dd128a01a"></a><br/></td></tr>
<tr class="separator:ga382ad6bedd6eecfdabd1b94dd128a01a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadf4252e600661fd762cfc0d1a9f5b892"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#gadf4252e600661fd762cfc0d1a9f5b892">NVIC_GetActive</a> (<a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a> IRQn)</td></tr>
<tr class="memdesc:gadf4252e600661fd762cfc0d1a9f5b892"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the interrupt active status [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#gadf4252e600661fd762cfc0d1a9f5b892"></a><br/></td></tr>
<tr class="separator:gadf4252e600661fd762cfc0d1a9f5b892"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5bb7f43ad92937c039dee3d36c3c2798"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#ga5bb7f43ad92937c039dee3d36c3c2798">NVIC_SetPriority</a> (<a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a> IRQn, uint32_t priority)</td></tr>
<tr class="memdesc:ga5bb7f43ad92937c039dee3d36c3c2798"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the priority for an interrupt.  <a href="#ga5bb7f43ad92937c039dee3d36c3c2798"></a><br/></td></tr>
<tr class="separator:ga5bb7f43ad92937c039dee3d36c3c2798"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab18fb9f6c5f4c70fdd73047f0f7c8395"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#gab18fb9f6c5f4c70fdd73047f0f7c8395">NVIC_GetPriority</a> (<a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a> IRQn)</td></tr>
<tr class="memdesc:gab18fb9f6c5f4c70fdd73047f0f7c8395"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the priority of an interrupt.  <a href="#gab18fb9f6c5f4c70fdd73047f0f7c8395"></a><br/></td></tr>
<tr class="separator:gab18fb9f6c5f4c70fdd73047f0f7c8395"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0688c59605b119c53c71b2505ab23eb5"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#ga0688c59605b119c53c71b2505ab23eb5">NVIC_EncodePriority</a> (uint32_t PriorityGroup, uint32_t PreemptPriority, uint32_t SubPriority)</td></tr>
<tr class="memdesc:ga0688c59605b119c53c71b2505ab23eb5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encodes Priority [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#ga0688c59605b119c53c71b2505ab23eb5"></a><br/></td></tr>
<tr class="separator:ga0688c59605b119c53c71b2505ab23eb5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad3cbca1be7a4726afa9448a9acd89377"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#gad3cbca1be7a4726afa9448a9acd89377">NVIC_DecodePriority</a> (uint32_t Priority, uint32_t PriorityGroup, uint32_t *pPreemptPriority, uint32_t *pSubPriority)</td></tr>
<tr class="memdesc:gad3cbca1be7a4726afa9448a9acd89377"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decode the interrupt priority [not for Cortex-M0, Cortex-M0+, or SC000].  <a href="#gad3cbca1be7a4726afa9448a9acd89377"></a><br/></td></tr>
<tr class="separator:gad3cbca1be7a4726afa9448a9acd89377"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1b47d17e90b6a03e7bd1ec6a0d549b46"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___n_v_i_c__gr.html#ga1b47d17e90b6a03e7bd1ec6a0d549b46">NVIC_SystemReset</a> (void)</td></tr>
<tr class="memdesc:ga1b47d17e90b6a03e7bd1ec6a0d549b46"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reset the system.  <a href="#ga1b47d17e90b6a03e7bd1ec6a0d549b46"></a><br/></td></tr>
<tr class="separator:ga1b47d17e90b6a03e7bd1ec6a0d549b46"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>ARM provides a template file <b>startup_<em>device</em></b> for each supported compiler. The file must be adapted by the silicon vendor to include interrupt vectors for all device-specific interrupt handlers. Each interrupt handler is defined as a <b><em>weak</em></b> function to an dummy handler. These interrupt handlers can be used directly in application software without being adapted by the programmer.</p>
<p>The table below describes the core exception names and their availability in various Cortex-M cores.</p>
<table  class="cmtable" summary="Core Exception Name">
<tr>
<th>Core Exception Name </th><th>IRQn Value </th><th>M0 </th><th>M0+ </th><th>M3 </th><th>M4 </th><th>M7 </th><th>SC000 </th><th>SC300 </th><th>Description  </th></tr>
<tr>
<td><b>NonMaskableInt_IRQn</b> </td><td>-14 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>Non Maskable Interrupt  </td></tr>
<tr>
<td><b>HardFault_IRQn</b> </td><td>-13 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>Hard Fault Interrupt  </td></tr>
<tr>
<td><b>MemoryManagement_IRQn</b> </td><td>-12 </td><td>&#160; </td><td>&#160; </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>&#160; </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>Memory Management Interrupt  </td></tr>
<tr>
<td><b>BusFault_IRQn</b> </td><td>-11 </td><td>&#160; </td><td>&#160; </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>&#160; </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>Bus Fault Interrupt  </td></tr>
<tr>
<td><b>UsageFault_IRQn</b> </td><td>-10 </td><td>&#160; </td><td>&#160; </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>&#160; </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>Usage Fault Interrupt  </td></tr>
<tr>
<td><b>SVCall_IRQn</b> </td><td>-5 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>SV Call Interrupt   </td></tr>
<tr>
<td><b>DebugMonitor_IRQn</b> </td><td>-4 </td><td>&#160; </td><td>&#160; </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>&#160; </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>Debug Monitor Interrupt  </td></tr>
<tr>
<td><b>PendSV_IRQn</b> </td><td>-2 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>Pend SV Interrupt  </td></tr>
<tr>
<td><b>SysTick_IRQn</b> </td><td>-1 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td><div class="image">
<img src="check.png"  alt="available"/>
</div>
 </td><td>System Tick Interrupt  </td></tr>
</table>
<h1><a class="anchor" id="cmsis_vectortable_M0_sec"></a>
For Cortex-M0, Cortex-M0+, or SC000</h1>
<p>The following exception names are fixed and define the start of the vector table for Cortex-M0, Cortex-M0+, or SC000:</p>
<div class="fragment"><div class="line">__Vectors       DCD     __initial_sp              ; Top of Stack</div>
<div class="line">                DCD     Reset_Handler             ; Reset Handler</div>
<div class="line">                DCD     NMI_Handler               ; NMI Handler</div>
<div class="line">                DCD     HardFault_Handler         ; Hard Fault Handler</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     SVC_Handler               ; SVCall Handler</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     PendSV_Handler            ; PendSV Handler</div>
<div class="line">                DCD     SysTick_Handler           ; SysTick Handler</div>
</div><!-- fragment --><h1><a class="anchor" id="cmsis_vectortable_M3_sec"></a>
For Cortex-M3</h1>
<p>The following exception names are fixed and define the start of the vector table for a Cortex-M3:</p>
<div class="fragment"><div class="line">__Vectors       DCD     __initial_sp              ; Top of Stack</div>
<div class="line">                DCD     Reset_Handler             ; Reset Handler</div>
<div class="line">                DCD     NMI_Handler               ; NMI Handler</div>
<div class="line">                DCD     HardFault_Handler         ; Hard Fault Handler</div>
<div class="line">                DCD     MemManage_Handler         ; MPU Fault Handler</div>
<div class="line">                DCD     BusFault_Handler          ; Bus Fault Handler</div>
<div class="line">                DCD     UsageFault_Handler        ; Usage Fault Handler</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     SVC_Handler               ; SVCall Handler</div>
<div class="line">                DCD     DebugMon_Handler          ; Debug Monitor Handler</div>
<div class="line">                DCD     0                         ; Reserved</div>
<div class="line">                DCD     PendSV_Handler            ; PendSV Handler</div>
<div class="line">                DCD     SysTick_Handler           ; SysTick Handler</div>
</div><!-- fragment --><h1><a class="anchor" id="cmsis_vectortable_ex_sec"></a>
Example</h1>
<p>The following is an examples for device-specific interrupts:</p>
<div class="fragment"><div class="line">; External Interrupts</div>
<div class="line">                DCD     WWDG_IRQHandler           ; Window Watchdog</div>
<div class="line">                DCD     PVD_IRQHandler            ; PVD through EXTI Line detect</div>
<div class="line">                DCD     TAMPER_IRQHandler         ; Tamper</div>
</div><!-- fragment --><p>Device-specific interrupts must have a dummy function that can be overwritten in user code. Below is an example for this dummy function.</p>
<div class="fragment"><div class="line">Default_Handler PROC</div>
<div class="line">                EXPORT WWDG_IRQHandler   [WEAK]</div>
<div class="line">                EXPORT PVD_IRQHandler    [WEAK]</div>
<div class="line">                EXPORT TAMPER_IRQHandler [WEAK]</div>
<div class="line">                :</div>
<div class="line">                :</div>
<div class="line">                WWDG_IRQHandler</div>
<div class="line">                PVD_IRQHandler</div>
<div class="line">                TAMPER_IRQHandler</div>
<div class="line">                :</div>
<div class="line">                :</div>
<div class="line">                B .</div>
<div class="line">                ENDP</div>
</div><!-- fragment --><p>The user application may simply define an interrupt handler function by using the handler name as shown below.</p>
<div class="fragment"><div class="line"><span class="keywordtype">void</span> WWDG_IRQHandler(<span class="keywordtype">void</span>)</div>
<div class="line">{</div>
<div class="line">  ...</div>
<div class="line">}</div>
</div><!-- fragment --><h1><a class="anchor" id="cmsis_vectortable_code_ex1_sec"></a>
Code Example 1</h1>
<p>The code below shows the usage of the CMSIS NVIC functions <a class="el" href="group___n_v_i_c__gr.html#gad78f447e891789b4d8f2e5b21eeda354" title="Set priority grouping [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_SetPriorityGrouping()</a>, <a class="el" href="group___n_v_i_c__gr.html#gaa81b19849367d3cdb95ac108c500fa78" title="Read the priority grouping [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_GetPriorityGrouping()</a>, <a class="el" href="group___n_v_i_c__gr.html#ga5bb7f43ad92937c039dee3d36c3c2798" title="Set the priority for an interrupt.">NVIC_SetPriority()</a>, <a class="el" href="group___n_v_i_c__gr.html#gab18fb9f6c5f4c70fdd73047f0f7c8395" title="Get the priority of an interrupt.">NVIC_GetPriority()</a>, <a class="el" href="group___n_v_i_c__gr.html#ga0688c59605b119c53c71b2505ab23eb5" title="Encodes Priority [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_EncodePriority()</a>, and <a class="el" href="group___n_v_i_c__gr.html#gad3cbca1be7a4726afa9448a9acd89377" title="Decode the interrupt priority [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_DecodePriority()</a> with an LPC1700.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &quot;LPC17xx.h&quot;</span></div>
<div class="line"></div>
<div class="line">uint32_t priorityGroup;                                     <span class="comment">/* Variables to store priority group and priority */</span></div>
<div class="line">uint32_t priority;</div>
<div class="line">uint32_t preemptPriority;</div>
<div class="line">uint32_t subPriority;</div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="keywordtype">int</span> main (<span class="keywordtype">void</span>)  {</div>
<div class="line"></div>
<div class="line">  <a class="code" href="group___n_v_i_c__gr.html#gad78f447e891789b4d8f2e5b21eeda354" title="Set priority grouping [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_SetPriorityGrouping</a>(5);                              <span class="comment">/* Set priority group to 5:</span></div>
<div class="line"><span class="comment">                                                               Bit[7..6] preempt priority Bits, </span></div>
<div class="line"><span class="comment">                                                               Bit[5..3] subpriority Bits </span></div>
<div class="line"><span class="comment">                                                               (valid for five priority bits) */</span></div>
<div class="line">     </div>
<div class="line">  priorityGroup =  <a class="code" href="group___n_v_i_c__gr.html#gaa81b19849367d3cdb95ac108c500fa78" title="Read the priority grouping [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_GetPriorityGrouping</a>();              <span class="comment">/* Get used priority grouping */</span></div>
<div class="line"></div>
<div class="line">  priority = <a class="code" href="group___n_v_i_c__gr.html#ga0688c59605b119c53c71b2505ab23eb5" title="Encodes Priority [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_EncodePriority</a>(priorityGroup, 1, 6);      <span class="comment">/* Encode priority with 6 for subpriority and 1 for preempt priority</span></div>
<div class="line"><span class="comment">                                                               Note: priority depends on the used priority grouping */</span></div>
<div class="line">                                                               </div>
<div class="line">  <a class="code" href="group___n_v_i_c__gr.html#ga5bb7f43ad92937c039dee3d36c3c2798" title="Set the priority for an interrupt.">NVIC_SetPriority</a>(UART0_IRQn, priority);                   <span class="comment">/* Set new priority */</span></div>
<div class="line"></div>
<div class="line">  priority =  <a class="code" href="group___n_v_i_c__gr.html#gab18fb9f6c5f4c70fdd73047f0f7c8395" title="Get the priority of an interrupt.">NVIC_GetPriority</a>(UART0_IRQn);                 <span class="comment">/* Retrieve priority again */</span>    </div>
<div class="line"></div>
<div class="line">  <a class="code" href="group___n_v_i_c__gr.html#gad3cbca1be7a4726afa9448a9acd89377" title="Decode the interrupt priority [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_DecodePriority</a>(priority, priorityGroup, &amp;preemptPriority, &amp;subPriority);</div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">while</span>(1);</div>
<div class="line">}</div>
</div><!-- fragment --><h1><a class="anchor" id="cmsis_vectortable_code_ex2_sec"></a>
Code Example 2</h1>
<p>The code below shows the usage of the CMSIS NVIC functions <a class="el" href="group___n_v_i_c__gr.html#ga530ad9fda2ed1c8b70e439ecfe80591f" title="Enable an external interrupt.">NVIC_EnableIRQ()</a>, <a class="el" href="group___n_v_i_c__gr.html#gadf4252e600661fd762cfc0d1a9f5b892" title="Get the interrupt active status [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_GetActive()</a> with an LPC1700.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &quot;LPC17xx.h&quot;</span></div>
<div class="line"></div>
<div class="line">uint32_t active;                                            <span class="comment">/* Variable to store interrupt active state */</span></div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="keywordtype">void</span> TIMER0_IRQHandler(<span class="keywordtype">void</span>)  {                             <span class="comment">/* Timer 0 interrupt handler  */</span></div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">if</span> (LPC_TIM0-&gt;IR &amp; (1 &lt;&lt; 0))  {                           <span class="comment">/* Check if interrupt for match channel 0 occured */</span> </div>
<div class="line">    LPC_TIM0-&gt;IR |= (1 &lt;&lt; 0);                               <span class="comment">/* Acknowledge interrupt for match channel 0 occured */</span></div>
<div class="line">  }</div>
<div class="line">  active = <a class="code" href="group___n_v_i_c__gr.html#gadf4252e600661fd762cfc0d1a9f5b892" title="Get the interrupt active status [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_GetActive</a>(TIMER0_IRQn);                     <span class="comment">/* Get interrupt active state of timer 0 */</span></div>
<div class="line">}</div>
<div class="line"></div>
<div class="line"></div>
<div class="line"><span class="keywordtype">int</span> main (<span class="keywordtype">void</span>) {</div>
<div class="line">                                                            <span class="comment">/* Set match channel register MR0 to 1 millisecond */</span></div>
<div class="line">  LPC_TIM0-&gt;MR0 = (((<a class="code" href="group__system__init__gr.html#gaa3cd3e43291e81e795d642b79b6088e6" title="Variable to hold the system core clock value.">SystemCoreClock</a> / 1000) / 4) - 1);     <span class="comment">/* 1 ms? */</span></div>
<div class="line">  </div>
<div class="line">  LPC_TIM0-&gt;MCR = (3 &lt;&lt; 0);                                 <span class="comment">/* Enable interrupt and reset for match channel MR0 */</span></div>
<div class="line"></div>
<div class="line">  <a class="code" href="group___n_v_i_c__gr.html#ga530ad9fda2ed1c8b70e439ecfe80591f" title="Enable an external interrupt.">NVIC_EnableIRQ</a>(TIMER0_IRQn);                              <span class="comment">/* Enable NVIC interrupt for timer 0 */</span></div>
<div class="line">  </div>
<div class="line">  LPC_TIM0-&gt;TCR = (1 &lt;&lt; 0);                                 <span class="comment">/* Enable timer 0 */</span></div>
<div class="line"></div>
<div class="line">  <span class="keywordflow">while</span>(1);</div>
<div class="line">}</div>
</div><!-- fragment --> <h2 class="groupheader">Enumeration Type Documentation</h2>
<a class="anchor" id="ga7e1129cd8a196f4284d41db3e82ad5c8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The core exception enumeration names for IRQn values are defined in the file <b>device.h</b>. </p>
<pre class="fragment">Negative IRQn values represent processor core exceptions (internal interrupts).
Positive IRQn values represent device-specific exceptions (external interrupts). 
The first device-specific interrupt has the IRQn value 0.
</pre><p>The table below describes the core exception names and their availability in various Cortex-M cores. </p>
<dl><dt><b>Enumerator: </b></dt><dd><table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" id="gga7e1129cd8a196f4284d41db3e82ad5c8ade177d9c70c89e084093024b932a4e30"></a>NonMaskableInt_IRQn</em>&nbsp;</td><td>
<p>Exception 2: Non Maskable Interrupt. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="gga7e1129cd8a196f4284d41db3e82ad5c8ab1a222a34a32f0ef5ac65e714efc1f85"></a>HardFault_IRQn</em>&nbsp;</td><td>
<p>Exception 3: Hard Fault Interrupt. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="gga7e1129cd8a196f4284d41db3e82ad5c8a33ff1cf7098de65d61b6354fee6cd5aa"></a>MemoryManagement_IRQn</em>&nbsp;</td><td>
<p>Exception 4: Memory Management Interrupt [not on Cortex-M0 variants]. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="gga7e1129cd8a196f4284d41db3e82ad5c8a8693500eff174f16119e96234fee73af"></a>BusFault_IRQn</em>&nbsp;</td><td>
<p>Exception 5: Bus Fault Interrupt [not on Cortex-M0 variants]. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="gga7e1129cd8a196f4284d41db3e82ad5c8a6895237c9443601ac832efa635dd8bbf"></a>UsageFault_IRQn</em>&nbsp;</td><td>
<p>Exception 6: Usage Fault Interrupt [not on Cortex-M0 variants]. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="gga7e1129cd8a196f4284d41db3e82ad5c8a4ce820b3cc6cf3a796b41aadc0cf1237"></a>SVCall_IRQn</em>&nbsp;</td><td>
<p>Exception 11: SV Call Interrupt. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="gga7e1129cd8a196f4284d41db3e82ad5c8a8e033fcef7aed98a31c60a7de206722c"></a>DebugMonitor_IRQn</em>&nbsp;</td><td>
<p>Exception 12: Debug Monitor Interrupt [not on Cortex-M0 variants]. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="gga7e1129cd8a196f4284d41db3e82ad5c8a03c3cc89984928816d81793fc7bce4a2"></a>PendSV_IRQn</em>&nbsp;</td><td>
<p>Exception 14: Pend SV Interrupt [not on Cortex-M0 variants]. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="gga7e1129cd8a196f4284d41db3e82ad5c8a6dbff8f8543325f3474cbae2446776e7"></a>SysTick_IRQn</em>&nbsp;</td><td>
<p>Exception 15: System Tick Interrupt. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="gga7e1129cd8a196f4284d41db3e82ad5c8aa62e040960b4beb6cba107e4703c12d2"></a>WWDG_STM_IRQn</em>&nbsp;</td><td>
<p>Device Interrupt 0: Window WatchDog Interrupt. </p>
</td></tr>
<tr><td valign="top"><em><a class="anchor" id="gga7e1129cd8a196f4284d41db3e82ad5c8a853e0f318108110e0527f29733d11f86"></a>PVD_STM_IRQn</em>&nbsp;</td><td>
<p>Device Interrupt 1: PVD through EXTI Line detection Interrupt. </p>
</td></tr>
</table>
</dd>
</dl>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga382ad6bedd6eecfdabd1b94dd128a01a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void NVIC_ClearPendingIRQ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a>&#160;</td>
          <td class="paramname"><em>IRQn</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function removes the pending state of the specified interrupt <em>IRQn</em>. <em>IRQn</em> cannot be a negative number.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">IRQn</td><td>Interrupt number</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>The registers that control the status of interrupts are called SETPEND and CLRPEND.</li>
<li>An interrupt can have the status pending though it is not active.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___n_v_i_c__gr.html#ga3b885147ef9965ecede49614de8df9d2">NVIC_SetPendingIRQ</a>; <a class="el" href="group___n_v_i_c__gr.html#ga95a8329a680b051ecf3ee8f516acc662" title="Get the pending interrupt.">NVIC_GetPendingIRQ</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gad3cbca1be7a4726afa9448a9acd89377"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void NVIC_DecodePriority </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>Priority</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>PriorityGroup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&#160;</td>
          <td class="paramname"><em>pPreemptPriority</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t *&#160;</td>
          <td class="paramname"><em>pSubPriority</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function decodes an interrupt priority value with the priority group <em>PriorityGroup</em> to preemptive priority value <em>pPreemptPriority</em> and subpriority value <em>pSubPriority</em>. In case of a conflict between priority grouping and available priority bits (__NVIC_PRIO_BITS) the smallest possible priority group is set.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">Priority</td><td>Priority </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">PriorityGroup</td><td>Priority group </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pPreemptPriority</td><td>Preemptive priority value (starting from 0) </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pSubPriority</td><td>Subpriority value (starting from 0)</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>not for Cortex-M0, Cortex-M0+, or SC000.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___n_v_i_c__gr.html#ga0688c59605b119c53c71b2505ab23eb5">NVIC_EncodePriority</a>; <a class="el" href="group___n_v_i_c__gr.html#gab18fb9f6c5f4c70fdd73047f0f7c8395" title="Get the priority of an interrupt.">NVIC_GetPriority</a>; <a class="el" href="group___n_v_i_c__gr.html#gaa81b19849367d3cdb95ac108c500fa78" title="Read the priority grouping [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_GetPriorityGrouping</a>;</li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga736ba13a76eb37ef6e2c253be8b0331c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void NVIC_DisableIRQ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a>&#160;</td>
          <td class="paramname"><em>IRQn</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function disables the specified device-specific interrupt <em>IRQn</em>. <em>IRQn</em> cannot be a negative value.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">IRQn</td><td>Number of the external interrupt to disable</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>The registers that control the enabling and disabling of interrupts are called SETENA and CLRENA.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___n_v_i_c__gr.html#ga530ad9fda2ed1c8b70e439ecfe80591f">NVIC_EnableIRQ</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga530ad9fda2ed1c8b70e439ecfe80591f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void NVIC_EnableIRQ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a>&#160;</td>
          <td class="paramname"><em>IRQn</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function enables the specified device-specific interrupt <em>IRQn</em>. <em>IRQn</em> cannot be a negative value.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">IRQn</td><td>Interrupt number</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>The registers that control the enabling and disabling of interrupts are called SETENA and CLRENA.</li>
<li>The number of supported interrupts depends on the implementation of the chip designer and can be read form the Interrupt Controller Type Register (ICTR) in granularities of 32: <br/>
 ICTR[4:0]<ul>
<li>=0 - 32 interrupts supported</li>
<li>=1 - 64 interrupts supported</li>
<li>...</li>
</ul>
</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___n_v_i_c__gr.html#ga736ba13a76eb37ef6e2c253be8b0331c">NVIC_DisableIRQ</a>; <a class="el" href="struct_s_cn_s_c_b___type.html" title="Structure type to access the System Control and ID Register not in the SCB.">SCnSCB_Type</a>;</li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga0688c59605b119c53c71b2505ab23eb5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t NVIC_EncodePriority </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>PriorityGroup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>PreemptPriority</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>SubPriority</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function encodes the priority for an interrupt with the priority group <em>PriorityGroup</em>, preemptive priority value <em>PreemptPriority</em>, and subpriority value <em>SubPriority</em>. In case of a conflict between priority grouping and available priority bits (__NVIC_PRIO_BITS) the smallest possible priority group is set.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">PriorityGroup</td><td>Priority group </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">PreemptPriority</td><td>Preemptive priority value (starting from 0) </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">SubPriority</td><td>Subpriority value (starting from 0)</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Encoded priority for the interrupt</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>not for Cortex-M0, Cortex-M0+, or SC000.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___n_v_i_c__gr.html#gad3cbca1be7a4726afa9448a9acd89377">NVIC_DecodePriority</a>; <a class="el" href="group___n_v_i_c__gr.html#ga5bb7f43ad92937c039dee3d36c3c2798" title="Set the priority for an interrupt.">NVIC_SetPriority</a>;</li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gadf4252e600661fd762cfc0d1a9f5b892"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t NVIC_GetActive </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a>&#160;</td>
          <td class="paramname"><em>IRQn</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function reads the Interrupt Active Register (NVIC_IABR0-NVIC_IABR7) in NVIC and returns the active bit of the interrupt <em>IRQn</em>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">IRQn</td><td>Interrupt number</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>=0 Interrupt is not active</li>
<li>=1 Interrupt is active, or active and pending</li>
</ul>
</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>not for Cortex-M0, Cortex-M0+, or SC000.</li>
<li>Each external interrupt has an active status bit. When the processor starts the interrupt handler the bit is set to 1 and cleared when the interrupt return is executed.</li>
<li>When an ISR is preempted and the processor executes anohter interrupt handler, the previous interrupt is still defined as active.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga95a8329a680b051ecf3ee8f516acc662"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t NVIC_GetPendingIRQ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a>&#160;</td>
          <td class="paramname"><em>IRQn</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function returns the pending status of the specified interrupt <em>IRQn</em>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">IRQn</td><td>Interrupt number</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><ul>
<li>=0 Interrupt is not pending</li>
<li>=1 Interrupt is pending</li>
</ul>
</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>The registers that control the status of interrupts are called SETPEND and CLRPEND.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___n_v_i_c__gr.html#ga3b885147ef9965ecede49614de8df9d2">NVIC_SetPendingIRQ</a>; <a class="el" href="group___n_v_i_c__gr.html#ga382ad6bedd6eecfdabd1b94dd128a01a" title="Clear an interrupt from pending.">NVIC_ClearPendingIRQ</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gab18fb9f6c5f4c70fdd73047f0f7c8395"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t NVIC_GetPriority </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a>&#160;</td>
          <td class="paramname"><em>IRQn</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function reads the priority for the specified interrupt <em>IRQn</em>. <em>IRQn</em> can can specify any device-specific (external) interrupt, or core (internal) interrupt.</p>
<p>The returned priority value is automatically aligned to the implemented priority bits of the microcontroller.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">IRQn</td><td>Interrupt number</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Interrupt priority</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>Each external interrupt has an associated priority-level register.</li>
<li>Unimplemented bits are read as zero.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___n_v_i_c__gr.html#ga5bb7f43ad92937c039dee3d36c3c2798">NVIC_SetPriority</a>; <a class="el" href="group___n_v_i_c__gr.html#gaa81b19849367d3cdb95ac108c500fa78" title="Read the priority grouping [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_GetPriorityGrouping</a>; <a class="el" href="group___core___register__gr.html#ga32da759f46e52c95bcfbde5012260667" title="Read the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__get_BASEPRI</a>;</li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gaa81b19849367d3cdb95ac108c500fa78"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t NVIC_GetPriorityGrouping </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This functuion returns the priority grouping (flag PRIGROUP in AIRCR[10:8]).</p>
<dl class="section return"><dt>Returns</dt><dd>Priority grouping field</dd></dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>not for Cortex-M0, Cortex-M0+, or SC000.</li>
<li>By default, priority group setting is zero.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___n_v_i_c__gr.html#gad78f447e891789b4d8f2e5b21eeda354">NVIC_SetPriorityGrouping</a>; <a class="el" href="group___n_v_i_c__gr.html#gab18fb9f6c5f4c70fdd73047f0f7c8395" title="Get the priority of an interrupt.">NVIC_GetPriority</a>; <a class="el" href="struct_s_c_b___type.html" title="Structure type to access the System Control Block (SCB).">SCB_Type</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga3b885147ef9965ecede49614de8df9d2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void NVIC_SetPendingIRQ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a>&#160;</td>
          <td class="paramname"><em>IRQn</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function sets the pending bit for the specified interrupt <em>IRQn</em>. <em>IRQn</em> cannot be a negative value.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">IRQn</td><td>Interrupt number</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>The registers that control the status of interrupts are called SETPEND and CLRPEND.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___n_v_i_c__gr.html#ga95a8329a680b051ecf3ee8f516acc662">NVIC_GetPendingIRQ</a>; <a class="el" href="group___n_v_i_c__gr.html#ga382ad6bedd6eecfdabd1b94dd128a01a" title="Clear an interrupt from pending.">NVIC_ClearPendingIRQ</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga5bb7f43ad92937c039dee3d36c3c2798"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void NVIC_SetPriority </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group___n_v_i_c__gr.html#ga7e1129cd8a196f4284d41db3e82ad5c8">IRQn_Type</a>&#160;</td>
          <td class="paramname"><em>IRQn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>priority</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets the priority for the interrupt specified by <em>IRQn</em>.<em>IRQn</em> can can specify any device-specific (external) interrupt, or core (internal) interrupt. The <em>priority</em> specifies the interrupt priority value, whereby lower values indicate a higher priority. The default priority is 0 for every interrupt. This is the highest possible priority.</p>
<p>The priority cannot be set for every core interrupt. HardFault and NMI have a fixed (negative) priority that is higher than any configurable exception or interrupt.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">IRQn</td><td>Interrupt Number </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">priority</td><td>Priority to set</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>The number of priority levels is configurable and depends on the implementation of the chip designer. To determine the number of bits implemented for interrupt priority-level registers, write <em>0xFF</em> to one of the priority-level register, then read back the value. For example, if the minimum number of 3 bits have been implemented, the read-back value is <em>0xE0</em>.</li>
<li>Writes to unimplemented bits are ignored.</li>
<li><b>For Cortex-M0</b>:<ul>
<li>Dynamic switching of interrupt priority levels is not supported. The priority level of an interrupt should not be changed after it has been enabled.</li>
<li>Supports 0 to 192 priority levels.</li>
<li>Priority-level registers are 2 bit wide, occupying the two MSBs. Each Interrupt Priority Level Register is 1-byte wide.</li>
</ul>
</li>
<li><b>For Cortex-M3, Cortex-M4, and Cortex-M7</b>:<ul>
<li>Dynamic switching of interrupt priority levels is supported.</li>
<li>Supports 0 to 255 priority levels.</li>
<li>Priority-level registers have a maximum width of 8 bits and a minumum of 3 bits. Each register can be further devided into preempt priority level and subpriority level.</li>
</ul>
</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___n_v_i_c__gr.html#gab18fb9f6c5f4c70fdd73047f0f7c8395">NVIC_GetPriority</a>; <a class="el" href="group___n_v_i_c__gr.html#gad78f447e891789b4d8f2e5b21eeda354" title="Set priority grouping [not for Cortex-M0, Cortex-M0+, or SC000].">NVIC_SetPriorityGrouping</a>; <a class="el" href="group___core___register__gr.html#ga360c73eb7ffb16088556f9278953b882" title="Set the BASEPRI register [not for Cortex-M0, Cortex-M0+, or SC000].">__set_BASEPRI</a>;</li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="gad78f447e891789b4d8f2e5b21eeda354"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void NVIC_SetPriorityGrouping </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>PriorityGroup</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The function sets the priority grouping <em>PriorityGroup</em> using the required unlock sequence. <em>PriorityGroup</em> is assigned to the field PRIGROUP (register AIRCR[10:8]). This field determines the split of group priority from subpriority. Only values from 0..7 are used. In case of a conflict between priority grouping and available priority bits (__NVIC_PRIO_BITS), the smallest possible priority group is set.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">PriorityGroup</td><td>Priority group</td></tr>
  </table>
  </dd>
</dl>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>not for Cortex-M0, Cortex-M0+, or SC000.</li>
<li>By default, priority group setting is zero.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="group___n_v_i_c__gr.html#gaa81b19849367d3cdb95ac108c500fa78">NVIC_GetPriorityGrouping</a>; <a class="el" href="group___n_v_i_c__gr.html#ga5bb7f43ad92937c039dee3d36c3c2798" title="Set the priority for an interrupt.">NVIC_SetPriority</a>; <a class="el" href="struct_s_c_b___type.html" title="Structure type to access the System Control Block (SCB).">SCB_Type</a></li>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
<a class="anchor" id="ga1b47d17e90b6a03e7bd1ec6a0d549b46"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void NVIC_SystemReset </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This function requests a system reset by setting the SYSRESETREQ flag in the AIRCR register.</p>
<dl class="section remark"><dt>Remarks</dt><dd><ul>
<li>In most microcontroller designs, setting the SYSRESETREQ flag resets the processor and most parts of the system, but should not affect the debug system.</li>
</ul>
</dd></dl>
<dl class="section see"><dt>See Also</dt><dd><ul>
<li><a class="el" href="index.html#ref_man_sec">Cortex-M Reference Manuals</a> </li>
</ul>
</dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
