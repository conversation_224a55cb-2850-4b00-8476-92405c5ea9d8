<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Register Mapping</title>
<title>CMSIS-CORE: Register Mapping</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_reg_map_pg.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Register Mapping </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>The table below associates some common register names used in CMSIS to the register names used in Technical Reference Manuals.</p>
<table  class="cmtable" summary="Register Mapping">
<tr>
<th>CMSIS Register Name </th><th>Cortex-M3, Cortex-M4, and Cortex-M7 </th><th>Cortex-M0 and Cortex-M0+ </th><th>Register Name  </th></tr>
<tr>
<th colspan="4">Nested Vectored Interrupt Controller (NVIC) Register Access  </th></tr>
<tr>
<td>NVIC-&gt;ISER[] </td><td>NVIC_ISER0..7 </td><td>ISER </td><td>Interrupt Set-Enable Registers  </td></tr>
<tr>
<td>NVIC-&gt;ICER[] </td><td>NVIC_ICER0..7 </td><td>ICER </td><td>Interrupt Clear-Enable Registers  </td></tr>
<tr>
<td>NVIC-&gt;ISPR[] </td><td>NVIC_ISPR0..7 </td><td>ISPR </td><td>Interrupt Set-Pending Registers  </td></tr>
<tr>
<td>NVIC-&gt;ICPR[] </td><td>NVIC_ICPR0..7 </td><td>ICPR </td><td>Interrupt Clear-Pending Registers  </td></tr>
<tr>
<td>NVIC-&gt;IABR[] </td><td>NVIC_IABR0..7 </td><td>- </td><td>Interrupt Active Bit Register  </td></tr>
<tr>
<td>NVIC-&gt;IP[] </td><td>NVIC_IPR0..59 </td><td>IPR0..7 </td><td>Interrupt Priority Register  </td></tr>
<tr>
<td>NVIC-&gt;STIR </td><td>STIR </td><td>- </td><td>Software Triggered Interrupt Register  </td></tr>
<tr>
<th colspan="4">System Control Block (SCB) Register Access  </th></tr>
<tr>
<td>SCB-&gt;CPUID </td><td>CPUID </td><td>CPUID </td><td>CPUID Base Register  </td></tr>
<tr>
<td>SCB-&gt;ICSR </td><td>ICSR </td><td>ICSR </td><td>Interrupt Control and State Register  </td></tr>
<tr>
<td>SCB-&gt;VTOR </td><td>VTOR </td><td>- </td><td>Vector Table Offset Register  </td></tr>
<tr>
<td>SCB-&gt;AIRCR </td><td>AIRCR </td><td>AIRCR </td><td>Application Interrupt and Reset Control Register  </td></tr>
<tr>
<td>SCB-&gt;SCR </td><td>SCR </td><td>SCR </td><td>System Control Register  </td></tr>
<tr>
<td>SCB-&gt;CCR </td><td>CCR </td><td>CCR </td><td>Configuration and Control Register  </td></tr>
<tr>
<td>SCB-&gt;SHP[] </td><td>SHPR1..3 </td><td>SHPR2..3 </td><td>System Handler Priority Registers  </td></tr>
<tr>
<td>SCB-&gt;SHCSR </td><td>SHCSR </td><td>SHCSR </td><td>System Handler Control and State Register  </td></tr>
<tr>
<td>SCB-&gt;CFSR </td><td>CFSR </td><td>- </td><td>Configurable Fault Status Registers  </td></tr>
<tr>
<td>SCB-&gt;HFSR </td><td>HFSR </td><td>- </td><td>HardFault Status Register  </td></tr>
<tr>
<td>SCB-&gt;DFSR </td><td>DFSR </td><td>- </td><td>Debug Fault Status Register  </td></tr>
<tr>
<td>SCB-&gt;MMFAR </td><td>MMFAR </td><td>- </td><td>MemManage Fault Address Register  </td></tr>
<tr>
<td>SCB-&gt;BFAR </td><td>BFAR </td><td>- </td><td>BusFault Address Register  </td></tr>
<tr>
<td>SCB-&gt;AFSR </td><td>AFSR </td><td>- </td><td>Auxiliary Fault Status Register  </td></tr>
<tr>
<td>SCB-&gt;PFR[] </td><td>ID_PFR0..1 </td><td>- </td><td>Processor Feature Registers  </td></tr>
<tr>
<td>SCB-&gt;DFR </td><td>ID_DFR0 </td><td>- </td><td>Debug Feature Register  </td></tr>
<tr>
<td>SCB-&gt;ADR </td><td>ID_AFR0 </td><td>- </td><td>Auxiliary Feature Register  </td></tr>
<tr>
<td>SCB-&gt;MMFR[] </td><td>ID_MMFR0..3 </td><td>- </td><td>Memory Model Feature Registers  </td></tr>
<tr>
<td>SCB-&gt;ISAR[] </td><td>ID_ISAR0..4 </td><td>- </td><td>Instruction Set Attributes Registers  </td></tr>
<tr>
<td>SCB-&gt;CPACR </td><td>CPACR </td><td>- </td><td>Coprocessor Access Control Register  </td></tr>
<tr>
<th colspan="4">System Control and ID Registers not in the SCB (SCnSCB) Register Access  </th></tr>
<tr>
<td>SCnSCB-&gt;ICTR </td><td>ICTR </td><td>- </td><td>Interrupt Controller Type Register  </td></tr>
<tr>
<td>SCnSCB-&gt;ACTLR </td><td>ACTLR </td><td>- </td><td>Auxiliary Control Register  </td></tr>
<tr>
<th colspan="4">System Timer (SysTick) Control and Status Register Access  </th></tr>
<tr>
<td>SysTick-&gt;CTRL </td><td>STCSR </td><td>SYST_CSR </td><td>SysTick Control and Status Register  </td></tr>
<tr>
<td>SysTick-&gt;LOAD </td><td>STRVR </td><td>SYST_RVR </td><td>SysTick Reload Value Register  </td></tr>
<tr>
<td>SysTick-&gt;VAL </td><td>STCVR </td><td>SYST_CVR </td><td>SysTick Current Value Register  </td></tr>
<tr>
<td>SysTick-&gt;CALIB </td><td>STCR </td><td>SYST_CALIB </td><td>SysTick Calibaration Value Register  </td></tr>
<tr>
<th colspan="4">Data Watchpoint and Trace (DWT) Register Access  </th></tr>
<tr>
<td>DWT-&gt;CTRL </td><td>DWT_CTRL </td><td>- </td><td>Control Register  </td></tr>
<tr>
<td>DWT-&gt;CYCCNT </td><td>DWT_CYCCNT </td><td>- </td><td>Cycle Count Register  </td></tr>
<tr>
<td>DWT-&gt;CPICNT </td><td>DWT_CPICNT </td><td>- </td><td>CPI Count Register  </td></tr>
<tr>
<td>DWT-&gt;EXCCNT </td><td>DWT_EXCCNT </td><td>- </td><td>Exception Overhead Count Register  </td></tr>
<tr>
<td>DWT-&gt;SLEEPCNT </td><td>DWT_SLEEPCNT </td><td>- </td><td>Sleep Count Register  </td></tr>
<tr>
<td>DWT-&gt;LSUCNT </td><td>DWT_LSUCNT </td><td>- </td><td>LSU Count Register  </td></tr>
<tr>
<td>DWT-&gt;FOLDCNT </td><td>DWT_FOLDCNT </td><td>- </td><td>Folded-instruction Count Register  </td></tr>
<tr>
<td>DWT-&gt;PCSR </td><td>DWT_PCSR </td><td>- </td><td>Program Counter Sample Register  </td></tr>
<tr>
<td>DWT-&gt;COMP0..3 </td><td>DWT_COMP0..3 </td><td>- </td><td>Comparator Register 0..3  </td></tr>
<tr>
<td>DWT-&gt;MASK0..3 </td><td>DWT_MASK0..3 </td><td>- </td><td>Mask Register 0..3  </td></tr>
<tr>
<td>DWT-&gt;FUNCTION0..3 </td><td>DWT_FUNCTION0..3 </td><td>- </td><td>Function Register 0..3  </td></tr>
<tr>
<th colspan="4">Instrumentation Trace Macrocell (ITM) Register Access  </th></tr>
<tr>
<td>ITM-&gt;PORT[] </td><td>ITM_STIM0..31 </td><td>- </td><td>Stimulus Port Registers  </td></tr>
<tr>
<td>ITM-&gt;TER </td><td>ITM_TER </td><td>- </td><td>Trace Enable Register  </td></tr>
<tr>
<td>ITM-&gt;TPR </td><td>ITM_TPR </td><td>- </td><td>ITM Trace Privilege Register  </td></tr>
<tr>
<td>ITM-&gt;TCR </td><td>ITM_TCR </td><td>- </td><td>Trace Control Register  </td></tr>
<tr>
<th colspan="4">Trace Port Interface (TPIU) Register Access  </th></tr>
<tr>
<td>TPI-&gt;SSPSR </td><td>TPIU_SSPR </td><td>- </td><td>Supported Parallel Port Size Register  </td></tr>
<tr>
<td>TPI-&gt;CSPSR </td><td>TPIU_CSPSR </td><td>- </td><td>Current Parallel Port Size Register  </td></tr>
<tr>
<td>TPI-&gt;ACPR </td><td>TPIU_ACPR </td><td>- </td><td>Asynchronous Clock Prescaler Register  </td></tr>
<tr>
<td>TPI-&gt;SPPR </td><td>TPIU_SPPR </td><td>- </td><td>Selected Pin Protocol Register  </td></tr>
<tr>
<td>TPI-&gt;FFSR </td><td>TPIU_FFSR </td><td>- </td><td>Formatter and Flush Status Register  </td></tr>
<tr>
<td>TPI-&gt;FFCR </td><td>TPIU_FFCR </td><td>- </td><td>Formatter and Flush Control Register  </td></tr>
<tr>
<td>TPI-&gt;FSCR </td><td>TPIU_FSCR </td><td>- </td><td>Formatter Synchronization Counter Register  </td></tr>
<tr>
<td>TPI-&gt;TRIGGER </td><td>TRIGGER </td><td>- </td><td>TRIGGER  </td></tr>
<tr>
<td>TPI-&gt;FIFO0 </td><td>FIFO data 0 </td><td>- </td><td>Integration ETM Data  </td></tr>
<tr>
<td>TPI-&gt;ITATBCTR2 </td><td>ITATBCTR2 </td><td>- </td><td>ITATBCTR2  </td></tr>
<tr>
<td>TPI-&gt;ITATBCTR0 </td><td>ITATBCTR0 </td><td>- </td><td>ITATBCTR0  </td></tr>
<tr>
<td>TPI-&gt;FIFO1 </td><td>FIFO data 1 </td><td>- </td><td>Integration ITM Data  </td></tr>
<tr>
<td>TPI-&gt;ITCTRL </td><td>TPIU_ITCTRL </td><td>- </td><td>Integration Mode Control  </td></tr>
<tr>
<td>TPI-&gt;CLAIMSET </td><td>CLAIMSET </td><td>- </td><td>Claim tag set  </td></tr>
<tr>
<td>TPI-&gt;CLAIMCLR </td><td>CLAIMCLR </td><td>- </td><td>Claim tag clear  </td></tr>
<tr>
<td>TPI-&gt;DEVID </td><td>TPIU_DEVID </td><td>- </td><td>TPIU_DEVID  </td></tr>
<tr>
<td>TPI-&gt;DEVTYPE </td><td>TPIU_DEVTYPE </td><td>- </td><td>TPIU_DEVTYPE  </td></tr>
<tr>
<th colspan="4">Memory Protection Unit (MPU) Register Access  </th></tr>
<tr>
<td>MPU-&gt;TYPE </td><td>MPU_TYPE </td><td>- </td><td>MPU Type Register  </td></tr>
<tr>
<td>MPU-&gt;CTRL </td><td>MPU_CTRL </td><td>- </td><td>MPU Control Register  </td></tr>
<tr>
<td>MPU-&gt;RNR </td><td>MPU_RNR </td><td>- </td><td>MPU Region Number Register  </td></tr>
<tr>
<td>MPU-&gt;RBAR </td><td>MPU_RBAR </td><td>- </td><td>MPU Region Base Address Register  </td></tr>
<tr>
<td>MPU-&gt;RASR </td><td>MPU_RASR </td><td>- </td><td>MPU Region Attribute and Size Register  </td></tr>
<tr>
<td>MPU-&gt;RBAR_A1..3 </td><td>MPU_RBAR_A1..3 </td><td>- </td><td>MPU alias Register  </td></tr>
<tr>
<td>MPU-&gt;RSAR_A1..3 </td><td>MPU_RSAR_A1..3 </td><td>- </td><td>MPU alias Register  </td></tr>
<tr>
<th colspan="4">Floating Point Unit (FPU) Register Access [only Cortex-M4 and Cortex-M7 both with FPU]  </th></tr>
<tr>
<td>FPU-&gt;FPCCR </td><td>FPCCR </td><td>- </td><td>FP Context Control Register  </td></tr>
<tr>
<td>FPU-&gt;FPCAR </td><td>FPCAR </td><td>- </td><td>FP Context Address Register  </td></tr>
<tr>
<td>FPU-&gt;FPDSCR </td><td>FPDSCR </td><td>- </td><td>FP Default Status Control Register  </td></tr>
<tr>
<td>FPU-&gt;MVFR0..1 </td><td>MVFR0..1 </td><td>- </td><td>Media and VFP Feature Registers  </td></tr>
</table>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
