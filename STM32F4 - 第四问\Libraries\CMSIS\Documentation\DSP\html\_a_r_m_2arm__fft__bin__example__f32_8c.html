<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_fft_bin_example_f32.c File Reference</title>
<title>CMSIS-DSP: arm_fft_bin_example_f32.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_a_r_m_2arm__fft__bin__example__f32_8c.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">ARM/arm_fft_bin_example_f32.c File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:abc004a7fade488e72310fd96c0a101dc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a></td></tr>
<tr class="separator:abc004a7fade488e72310fd96c0a101dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a52d2cba30e6946c95578be946ac12a65"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main</a> (void)</td></tr>
<tr class="separator:a52d2cba30e6946c95578be946ac12a65"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a3d8ecb82590486ceebccc76263963b16"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a3d8ecb82590486ceebccc76263963b16">testInput_f32_10khz</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td></tr>
<tr class="separator:a3d8ecb82590486ceebccc76263963b16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaaf907bde12051bc8bb5d48b7d315a35"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#aaaf907bde12051bc8bb5d48b7d315a35">testOutput</a> [<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>/2]</td></tr>
<tr class="separator:aaaf907bde12051bc8bb5d48b7d315a35"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b500899c581f6df3ffc0a9f3a9ef6aa"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a9b500899c581f6df3ffc0a9f3a9ef6aa">fftSize</a></td></tr>
<tr class="separator:a9b500899c581f6df3ffc0a9f3a9ef6aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a379ccb99013d369a41b49619083c16ef"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a></td></tr>
<tr class="separator:a379ccb99013d369a41b49619083c16ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d2e31c38e8172505e0a369a6898657d"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a4d2e31c38e8172505e0a369a6898657d">doBitReverse</a></td></tr>
<tr class="separator:a4d2e31c38e8172505e0a369a6898657d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec50b0f697fd9e7e7e244487a4bd57e4"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#aec50b0f697fd9e7e7e244487a4bd57e4">refIndex</a></td></tr>
<tr class="separator:aec50b0f697fd9e7e7e244487a4bd57e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4a391651dbb95db35ccba70fc4f9e049"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a4a391651dbb95db35ccba70fc4f9e049">testIndex</a></td></tr>
<tr class="separator:a4a391651dbb95db35ccba70fc4f9e049"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="abc004a7fade488e72310fd96c0a101dc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define TEST_LENGTH_SAMPLES</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="a52d2cba30e6946c95578be946ac12a65"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t main </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>References <a class="el" href="group___complex_f_f_t.html#gade0f9c4ff157b6b9c72a1eafd86ebf80">arm_cfft_f32()</a>, <a class="el" href="arm__const__structs_8c.html#a05abc294a9159abbd6ffb4f188fe18b1">arm_cfft_sR_f32_len1024</a>, <a class="el" href="group__cmplx__mag.html#gae45024c497392cde2ae358a76d435213">arm_cmplx_mag_f32()</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a9f8b2a10bd827fb4600e77d455902eb0">ARM_MATH_SUCCESS</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>, <a class="el" href="group___max.html#ga5b89d1b04575aeec494f678695fb87d8">arm_max_f32()</a>, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a4d2e31c38e8172505e0a369a6898657d">doBitReverse</a>, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a9b500899c581f6df3ffc0a9f3a9ef6aa">fftSize</a>, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a379ccb99013d369a41b49619083c16ef">ifftFlag</a>, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#aec50b0f697fd9e7e7e244487a4bd57e4">refIndex</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a4a391651dbb95db35ccba70fc4f9e049">testIndex</a>, <a class="el" href="_a_r_m_2arm__fft__bin__data_8c.html#a8a12a8ec4b866be84f4b7d3daf6a1242">testInput_f32_10khz</a>, and <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>.</p>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="a4d2e31c38e8172505e0a369a6898657d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t doBitReverse</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fft_bin_example_f32_8c-example.html#a5">arm_fft_bin_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a9b500899c581f6df3ffc0a9f3a9ef6aa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t fftSize</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fft_bin_example_f32_8c-example.html#a3">arm_fft_bin_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a379ccb99013d369a41b49619083c16ef"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t ifftFlag</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fft_bin_example_f32_8c-example.html#a4">arm_fft_bin_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="group___complex_f_f_t.html#gac9565e6bc7229577ecf5e090313cafd7">arm_cfft_radix2_init_f32()</a>, <a class="el" href="group___complex_f_f_t.html#ga5c5b2127b3c4ea2d03692127f8543858">arm_cfft_radix2_init_q15()</a>, <a class="el" href="group___complex_f_f_t.html#gabec9611e77382f31e152668bf6b4b638">arm_cfft_radix2_init_q31()</a>, <a class="el" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a">arm_cfft_radix4_init_f32()</a>, <a class="el" href="group___complex_f_f_t.html#ga0c2acfda3126c452e75b81669e8ad9ef">arm_cfft_radix4_init_q15()</a>, <a class="el" href="group___complex_f_f_t.html#gad5caaafeec900c8ff72321c01bbd462c">arm_cfft_radix4_init_q31()</a>, and <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="aec50b0f697fd9e7e7e244487a4bd57e4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t refIndex</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fft_bin_example_f32_8c-example.html#a6">arm_fft_bin_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a4a391651dbb95db35ccba70fc4f9e049"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t testIndex</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_fft_bin_example_f32_8c-example.html#a7">arm_fft_bin_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a3d8ecb82590486ceebccc76263963b16"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> testInput_f32_10khz[<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aaaf907bde12051bc8bb5d48b7d315a35"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> testOutput[<a class="el" href="arm__signal__converge__example__f32_8c.html#abc004a7fade488e72310fd96c0a101dc">TEST_LENGTH_SAMPLES</a>/2]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_be2d3df67661aefe0e3f0071a1d6f8f1.html">DSP_Lib</a></li><li class="navelem"><a class="el" href="dir_50f4d4f91ce5cd72cb6928b47e85a7f8.html">Examples</a></li><li class="navelem"><a class="el" href="dir_38d31328c42027cc5452e7496de7b88f.html">arm_fft_bin_example</a></li><li class="navelem"><a class="el" href="dir_59a4e177523ec142602b8d9a69e8f1ad.html">ARM</a></li><li class="navelem"><a class="el" href="_a_r_m_2arm__fft__bin__example__f32_8c.html">arm_fft_bin_example_f32.c</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:49 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
