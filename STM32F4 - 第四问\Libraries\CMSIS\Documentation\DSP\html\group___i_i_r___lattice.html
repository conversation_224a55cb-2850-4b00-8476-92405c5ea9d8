<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Infinite Impulse Response (IIR) Lattice Filters</title>
<title>CMSIS-DSP: Infinite Impulse Response (IIR) Lattice Filters</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___i_i_r___lattice.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Infinite Impulse Response (IIR) Lattice Filters</div>  </div>
<div class="ingroups"><a class="el" href="group__group_filters.html">Filtering Functions</a></div></div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga56164a0fe48619b8ceec160347bdd2ff"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___i_i_r___lattice.html#ga56164a0fe48619b8ceec160347bdd2ff">arm_iir_lattice_f32</a> (const <a class="el" href="structarm__iir__lattice__instance__f32.html">arm_iir_lattice_instance_f32</a> *S, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pSrc, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga56164a0fe48619b8ceec160347bdd2ff"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the floating-point IIR lattice filter.  <a href="#ga56164a0fe48619b8ceec160347bdd2ff"></a><br/></td></tr>
<tr class="separator:ga56164a0fe48619b8ceec160347bdd2ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaed3b0230bb77439dc902daa625985e04"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___i_i_r___lattice.html#gaed3b0230bb77439dc902daa625985e04">arm_iir_lattice_init_f32</a> (<a class="el" href="structarm__iir__lattice__instance__f32.html">arm_iir_lattice_instance_f32</a> *S, uint16_t numStages, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pkCoeffs, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pvCoeffs, <a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *pState, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gaed3b0230bb77439dc902daa625985e04"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the floating-point IIR lattice filter.  <a href="#gaed3b0230bb77439dc902daa625985e04"></a><br/></td></tr>
<tr class="separator:gaed3b0230bb77439dc902daa625985e04"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1f4bc2dd3d5641e96815d3a5aad58998"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___i_i_r___lattice.html#ga1f4bc2dd3d5641e96815d3a5aad58998">arm_iir_lattice_init_q15</a> (<a class="el" href="structarm__iir__lattice__instance__q15.html">arm_iir_lattice_instance_q15</a> *S, uint16_t numStages, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pkCoeffs, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pvCoeffs, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pState, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga1f4bc2dd3d5641e96815d3a5aad58998"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q15 IIR lattice filter.  <a href="#ga1f4bc2dd3d5641e96815d3a5aad58998"></a><br/></td></tr>
<tr class="separator:ga1f4bc2dd3d5641e96815d3a5aad58998"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab686c14175581797d9c3ad7bf1d5cc1e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___i_i_r___lattice.html#gab686c14175581797d9c3ad7bf1d5cc1e">arm_iir_lattice_init_q31</a> (<a class="el" href="structarm__iir__lattice__instance__q31.html">arm_iir_lattice_instance_q31</a> *S, uint16_t numStages, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pkCoeffs, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pvCoeffs, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pState, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gab686c14175581797d9c3ad7bf1d5cc1e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialization function for the Q31 IIR lattice filter.  <a href="#gab686c14175581797d9c3ad7bf1d5cc1e"></a><br/></td></tr>
<tr class="separator:gab686c14175581797d9c3ad7bf1d5cc1e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeb9e9599a288832ed123183eaa8b294a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___i_i_r___lattice.html#gaeb9e9599a288832ed123183eaa8b294a">arm_iir_lattice_q15</a> (const <a class="el" href="structarm__iir__lattice__instance__q15.html">arm_iir_lattice_instance_q15</a> *S, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pSrc, <a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:gaeb9e9599a288832ed123183eaa8b294a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q15 IIR lattice filter.  <a href="#gaeb9e9599a288832ed123183eaa8b294a"></a><br/></td></tr>
<tr class="separator:gaeb9e9599a288832ed123183eaa8b294a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga123b26fa9156cd8d3622dd85931741ed"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group___i_i_r___lattice.html#ga123b26fa9156cd8d3622dd85931741ed">arm_iir_lattice_q31</a> (const <a class="el" href="structarm__iir__lattice__instance__q31.html">arm_iir_lattice_instance_q31</a> *S, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pSrc, <a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *pDst, uint32_t <a class="el" href="arm__variance__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>)</td></tr>
<tr class="memdesc:ga123b26fa9156cd8d3622dd85931741ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">Processing function for the Q31 IIR lattice filter.  <a href="#ga123b26fa9156cd8d3622dd85931741ed"></a><br/></td></tr>
<tr class="separator:ga123b26fa9156cd8d3622dd85931741ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>This set of functions implements lattice filters for Q15, Q31 and floating-point data types. Lattice filters are used in a variety of adaptive filter applications. The filter structure has feedforward and feedback components and the net impulse response is infinite length. The functions operate on blocks of input and output data and each call to the function processes <code>blockSize</code> samples through the filter. <code>pSrc</code> and <code>pDst</code> point to input and output arrays containing <code>blockSize</code> values.</p>
<dl class="section user"><dt>Algorithm: </dt><dd><div class="image">
<img src="IIRLattice.gif" alt="IIRLattice.gif"/>
<div class="caption">
Infinite Impulse Response Lattice filter</div></div>
 <pre>    
     fN(n)   =  x(n)    
     fm-1(n) = fm(n) - km * gm-1(n-1)   for m = N, N-1, ...1    
     gm(n)   = km * fm-1(n) + gm-1(n-1) for m = N, N-1, ...1    
     y(n)    = vN * gN(n) + vN-1 * gN-1(n) + ...+ v0 * g0(n)    
  </pre> </dd></dl>
<dl class="section user"><dt></dt><dd><code>pkCoeffs</code> points to array of reflection coefficients of size <code>numStages</code>. Reflection coefficients are stored in time-reversed order. </dd></dl>
<dl class="section user"><dt></dt><dd><pre>    
     {kN, kN-1, ....k1}    
  </pre> <code>pvCoeffs</code> points to the array of ladder coefficients of size <code>(numStages+1)</code>. Ladder coefficients are stored in time-reversed order. </dd></dl>
<dl class="section user"><dt></dt><dd><pre>    
     {vN, vN-1, ...v0}    
  </pre> <code>pState</code> points to a state array of size <code>numStages + blockSize</code>. The state variables shown in the figure above (the g values) are stored in the <code>pState</code> array. The state variables are updated after each block of data is processed; the coefficients are untouched. </dd></dl>
<dl class="section user"><dt>Instance Structure </dt><dd>The coefficients and state variables for a filter are stored together in an instance data structure. A separate instance structure must be defined for each filter. Coefficient arrays may be shared among several instances while state variable arrays cannot be shared. There are separate instance structure declarations for each of the 3 supported data types.</dd></dl>
<dl class="section user"><dt>Initialization Functions </dt><dd>There is also an associated initialization function for each data type. The initialization function performs the following operations:<ul>
<li>Sets the values of the internal structure fields.</li>
<li>Zeros out the values in the state buffer. To do this manually without calling the init function, assign the follow subfields of the instance structure: numStages, pkCoeffs, pvCoeffs, pState. Also set all of the values in pState to zero.</li>
</ul>
</dd></dl>
<dl class="section user"><dt></dt><dd>Use of the initialization function is optional. However, if the initialization function is used, then the instance structure cannot be placed into a const data section. To place an instance structure into a const data section, the instance structure must be manually initialized. Set the values in the state buffer to zeros and then manually initialize the instance structure as follows: <pre>    
*arm_iir_lattice_instance_f32 S = {numStages, pState, pkCoeffs, pvCoeffs};    
*arm_iir_lattice_instance_q31 S = {numStages, pState, pkCoeffs, pvCoeffs};    
*arm_iir_lattice_instance_q15 S = {numStages, pState, pkCoeffs, pvCoeffs};    
  </pre> </dd></dl>
<dl class="section user"><dt></dt><dd>where <code>numStages</code> is the number of stages in the filter; <code>pState</code> points to the state buffer array; <code>pkCoeffs</code> points to array of the reflection coefficients; <code>pvCoeffs</code> points to the array of ladder coefficients. </dd></dl>
<dl class="section user"><dt>Fixed-Point Behavior </dt><dd>Care must be taken when using the fixed-point versions of the IIR lattice filter functions. In particular, the overflow and saturation behavior of the accumulator used in each function must be considered. Refer to the function specific documentation below for usage guidelines. </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga56164a0fe48619b8ceec160347bdd2ff"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_iir_lattice_f32 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__iir__lattice__instance__f32.html">arm_iir_lattice_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point IIR lattice structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__iir__lattice__instance__f32.html#af8de449af5efe1f30be82f9ba35587ee">arm_iir_lattice_instance_f32::numStages</a>, <a class="el" href="structarm__iir__lattice__instance__f32.html#aa69fcdd3775e828d450ce1bbd978fa31">arm_iir_lattice_instance_f32::pkCoeffs</a>, <a class="el" href="structarm__iir__lattice__instance__f32.html#a30babe7815510219e6e3d28e6e4a5969">arm_iir_lattice_instance_f32::pState</a>, and <a class="el" href="structarm__iir__lattice__instance__f32.html#afc7c8f577e6f27d097fe55f57e707f72">arm_iir_lattice_instance_f32::pvCoeffs</a>.</p>

</div>
</div>
<a class="anchor" id="gaed3b0230bb77439dc902daa625985e04"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_iir_lattice_init_f32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__iir__lattice__instance__f32.html">arm_iir_lattice_instance_f32</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numStages</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pkCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pvCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the floating-point IIR lattice structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numStages</td><td>number of stages in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pkCoeffs</td><td>points to the reflection coefficient buffer. The array is of length numStages. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pvCoeffs</td><td>points to the ladder coefficient buffer. The array is of length numStages+1. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. The array is of length numStages+blockSize. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="structarm__iir__lattice__instance__f32.html#af8de449af5efe1f30be82f9ba35587ee">arm_iir_lattice_instance_f32::numStages</a>, <a class="el" href="structarm__iir__lattice__instance__f32.html#aa69fcdd3775e828d450ce1bbd978fa31">arm_iir_lattice_instance_f32::pkCoeffs</a>, <a class="el" href="structarm__iir__lattice__instance__f32.html#a30babe7815510219e6e3d28e6e4a5969">arm_iir_lattice_instance_f32::pState</a>, and <a class="el" href="structarm__iir__lattice__instance__f32.html#afc7c8f577e6f27d097fe55f57e707f72">arm_iir_lattice_instance_f32::pvCoeffs</a>.</p>

</div>
</div>
<a class="anchor" id="ga1f4bc2dd3d5641e96815d3a5aad58998"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_iir_lattice_init_q15 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__iir__lattice__instance__q15.html">arm_iir_lattice_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numStages</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pkCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pvCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 IIR lattice structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numStages</td><td>number of stages in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pkCoeffs</td><td>points to reflection coefficient buffer. The array is of length numStages. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pvCoeffs</td><td>points to ladder coefficient buffer. The array is of length numStages+1. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to state buffer. The array is of length numStages+blockSize. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process per call. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="structarm__iir__lattice__instance__q15.html#a96fbed313bef01070409fa182d26ba3f">arm_iir_lattice_instance_q15::numStages</a>, <a class="el" href="structarm__iir__lattice__instance__q15.html#a41c214a1ec38d4a82fae8899d715dd29">arm_iir_lattice_instance_q15::pkCoeffs</a>, <a class="el" href="structarm__iir__lattice__instance__q15.html#afd0136ab917b529554d93f41a5e04618">arm_iir_lattice_instance_q15::pState</a>, and <a class="el" href="structarm__iir__lattice__instance__q15.html#a4c4f57f45b223abbe2a9fb727bd2cad9">arm_iir_lattice_instance_q15::pvCoeffs</a>.</p>

</div>
</div>
<a class="anchor" id="gab686c14175581797d9c3ad7bf1d5cc1e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_iir_lattice_init_q31 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structarm__iir__lattice__instance__q31.html">arm_iir_lattice_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>numStages</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pkCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pvCoeffs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pState</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q31 IIR lattice structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">numStages</td><td>number of stages in the filter. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pkCoeffs</td><td>points to the reflection coefficient buffer. The array is of length numStages. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pvCoeffs</td><td>points to the ladder coefficient buffer. The array is of length numStages+1. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pState</td><td>points to the state buffer. The array is of length numStages+blockSize. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none. </dd></dl>

<p>References <a class="el" href="structarm__iir__lattice__instance__q31.html#a9df4570ed28c50fd9193ab654ff236ad">arm_iir_lattice_instance_q31::numStages</a>, <a class="el" href="structarm__iir__lattice__instance__q31.html#a1d30aa16aac7722936ea9dee59211863">arm_iir_lattice_instance_q31::pkCoeffs</a>, <a class="el" href="structarm__iir__lattice__instance__q31.html#a941282745effd26a889fbfadf4b95e6a">arm_iir_lattice_instance_q31::pState</a>, and <a class="el" href="structarm__iir__lattice__instance__q31.html#a04507e2b982b1dfa97b7b55752dea6b9">arm_iir_lattice_instance_q31::pvCoeffs</a>.</p>

</div>
</div>
<a class="anchor" id="gaeb9e9599a288832ed123183eaa8b294a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_iir_lattice_q15 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__iir__lattice__instance__q15.html">arm_iir_lattice_instance_q15</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#ab5a8fb21a5b3b983d5f54f31614052ea">q15_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q15 IIR lattice structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function is implemented using a 64-bit internal accumulator. Both coefficients and state variables are represented in 1.15 format and multiplications yield a 2.30 result. The 2.30 intermediate results are accumulated in a 64-bit accumulator in 34.30 format. There is no risk of internal overflow with this approach and the full precision of intermediate multiplications is preserved. After all additions have been performed, the accumulator is truncated to 34.15 format by discarding low 15 bits. Lastly, the accumulator is saturated to yield a result in 1.15 format. </dd></dl>

<p>References <a class="el" href="arm__math_8h.html#a9de2e0a5785be82866bcb96012282248">__SIMD32</a>, <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="structarm__iir__lattice__instance__q15.html#a96fbed313bef01070409fa182d26ba3f">arm_iir_lattice_instance_q15::numStages</a>, <a class="el" href="structarm__iir__lattice__instance__q15.html#a41c214a1ec38d4a82fae8899d715dd29">arm_iir_lattice_instance_q15::pkCoeffs</a>, <a class="el" href="structarm__iir__lattice__instance__q15.html#afd0136ab917b529554d93f41a5e04618">arm_iir_lattice_instance_q15::pState</a>, and <a class="el" href="structarm__iir__lattice__instance__q15.html#a4c4f57f45b223abbe2a9fb727bd2cad9">arm_iir_lattice_instance_q15::pvCoeffs</a>.</p>

</div>
</div>
<a class="anchor" id="ga123b26fa9156cd8d3622dd85931741ed"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void arm_iir_lattice_q31 </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structarm__iir__lattice__instance__q31.html">arm_iir_lattice_instance_q31</a> *&#160;</td>
          <td class="paramname"><em>S</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pSrc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="arm__math_8h.html#adc89a3547f5324b7b3b95adec3806bc0">q31_t</a> *&#160;</td>
          <td class="paramname"><em>pDst</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>blockSize</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*S</td><td>points to an instance of the Q31 IIR lattice structure. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">*pSrc</td><td>points to the block of input data. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">*pDst</td><td>points to the block of output data. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">blockSize</td><td>number of samples to process. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>none.</dd></dl>
<p><b>Scaling and Overflow Behavior:</b> </p>
<dl class="section user"><dt></dt><dd>The function is implemented using an internal 64-bit accumulator. The accumulator has a 2.62 format and maintains full precision of the intermediate multiplication results but provides only a single guard bit. Thus, if the accumulator result overflows it wraps around rather than clip. In order to avoid overflows completely the input signal must be scaled down by 2*log2(numStages) bits. After all multiply-accumulates are performed, the 2.62 accumulator is saturated to 1.32 format and then truncated to 1.31 format. </dd></dl>

<p>References <a class="el" href="arm__fir__example__f32_8c.html#ab6558f40a619c2502fbc24c880fd4fb0">blockSize</a>, <a class="el" href="arm__math_8h.html#ad7373e53d3c2e1adfeafc8c2e9720b5c">clip_q63_to_q31()</a>, <a class="el" href="structarm__iir__lattice__instance__q31.html#a9df4570ed28c50fd9193ab654ff236ad">arm_iir_lattice_instance_q31::numStages</a>, <a class="el" href="structarm__iir__lattice__instance__q31.html#a1d30aa16aac7722936ea9dee59211863">arm_iir_lattice_instance_q31::pkCoeffs</a>, <a class="el" href="structarm__iir__lattice__instance__q31.html#a941282745effd26a889fbfadf4b95e6a">arm_iir_lattice_instance_q31::pState</a>, and <a class="el" href="structarm__iir__lattice__instance__q31.html#a04507e2b982b1dfa97b7b55752dea6b9">arm_iir_lattice_instance_q31::pvCoeffs</a>.</p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
