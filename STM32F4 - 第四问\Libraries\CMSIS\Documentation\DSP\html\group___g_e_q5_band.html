<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Graphic Audio Equalizer Example</title>
<title>CMSIS-DSP: Graphic Audio Equalizer Example</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('group___g_e_q5_band.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Graphic Audio Equalizer Example</div>  </div>
<div class="ingroups"><a class="el" href="group__group_examples.html">Examples</a></div></div><!--header-->
<div class="contents">
<dl class="section user"><dt>Description:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>This example demonstrates how a 5-band graphic equalizer can be constructed using the Biquad cascade functions. A graphic equalizer is used in audio applications to vary the tonal quality of the audio.</dd></dl>
<dl class="section user"><dt>Block Diagram:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd>The design is based on a cascade of 5 filter sections. <div class="image">
<img src="GEQ_signalflow.gif" alt="GEQ_signalflow.gif"/>
</div>
 Each filter section is 4th order and consists of a cascade of two Biquads. Each filter has a nominal gain of 0 dB (1.0 in linear units) and boosts or cuts signals within a specific frequency range. The edge frequencies between the 5 bands are 100, 500, 2000, and 6000 Hz. Each band has an adjustable boost or cut in the range of +/- 9 dB. For example, the band that extends from 500 to 2000 Hz has the response shown below: </dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="GEQ_bandresponse.gif" alt="GEQ_bandresponse.gif"/>
</div>
 </dd></dl>
<dl class="section user"><dt></dt><dd>With 1 dB steps, each filter has a total of 19 different settings. The filter coefficients for all possible 19 settings were precomputed in MATLAB and stored in a table. With 5 different tables, there are a total of 5 x 19 = 95 different 4th order filters. All 95 responses are shown below: </dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="GEQ_allbandresponse.gif" alt="GEQ_allbandresponse.gif"/>
</div>
 </dd></dl>
<dl class="section user"><dt></dt><dd>Each 4th order filter has 10 coefficents for a grand total of 950 different filter coefficients that must be tabulated. The input and output data is in Q31 format. For better noise performance, the two low frequency bands are implemented using the high precision 32x64-bit Biquad filters. The remaining 3 high frequency bands use standard 32x32-bit Biquad filters. The input signal used in the example is a logarithmic chirp. </dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="GEQ_inputchirp.gif" alt="GEQ_inputchirp.gif"/>
</div>
 </dd></dl>
<dl class="section user"><dt></dt><dd>The array <code>bandGains</code> specifies the gain in dB to apply in each band. For example, if <code>bandGains={0, -3, 6, 4, -6};</code> then the output signal will be: </dd></dl>
<dl class="section user"><dt></dt><dd><div class="image">
<img src="GEQ_outputchirp.gif" alt="GEQ_outputchirp.gif"/>
</div>
 </dd></dl>
<dl class="section user"><dt></dt><dd></dd></dl>
<dl class="section note"><dt>Note</dt><dd>The output chirp signal follows the gain or boost of each band. </dd></dl>
<dl class="section user"><dt></dt><dd></dd></dl>
<dl class="section user"><dt>Variables Description:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd><ul>
<li><code>testInput_f32</code> points to the input data </li>
<li><code>testRefOutput_f32</code> points to the reference output data </li>
<li><code>testOutput</code> points to the test output data </li>
<li><code>inputQ31</code> temporary input buffer </li>
<li><code>outputQ31</code> temporary output buffer </li>
<li><code>biquadStateBand1Q31</code> points to state buffer for band1 </li>
<li><code>biquadStateBand2Q31</code> points to state buffer for band2 </li>
<li><code>biquadStateBand3Q31</code> points to state buffer for band3 </li>
<li><code>biquadStateBand4Q31</code> points to state buffer for band4 </li>
<li><code>biquadStateBand5Q31</code> points to state buffer for band5 </li>
<li><code>coeffTable</code> points to coefficient buffer for all bands </li>
<li><code>gainDB</code> gain buffer which has gains applied for all the bands</li>
</ul>
</dd></dl>
<dl class="section user"><dt>CMSIS DSP Software Library Functions Used:</dt><dd></dd></dl>
<dl class="section user"><dt></dt><dd><ul>
<li><a class="el" href="group___biquad_cascade_d_f1__32x64.html#ga44900cecb8083afcaabf905ffcd656bb">arm_biquad_cas_df1_32x64_init_q31()</a></li>
<li><a class="el" href="group___biquad_cascade_d_f1__32x64.html#ga953a83e69685de6575cff37feb358a93">arm_biquad_cas_df1_32x64_q31()</a></li>
<li><a class="el" href="group___biquad_cascade_d_f1.html#gaf42a44f9b16d61e636418c83eefe577b" title="Initialization function for the Q31 Biquad cascade filter.">arm_biquad_cascade_df1_init_q31()</a></li>
<li><a class="el" href="group___biquad_cascade_d_f1.html#ga27b0c54da702713976e5202d20b4473f" title="Processing function for the Q31 Biquad cascade filter.">arm_biquad_cascade_df1_q31()</a></li>
<li><a class="el" href="group__scale.html#ga83e36cd82bf51ce35406a199e477d47c" title="Multiplies a Q31 vector by a scalar.">arm_scale_q31()</a></li>
<li><a class="el" href="group__scale.html#ga3487af88b112f682ee90589cd419e123" title="Multiplies a floating-point vector by a scalar.">arm_scale_f32()</a></li>
<li><a class="el" href="group__float__to__x.html#ga177704107f94564e9abe4daaa36f4554" title="Converts the elements of the floating-point vector to Q31 vector.">arm_float_to_q31()</a></li>
<li><a class="el" href="group__q31__to__x.html#gacf407b007a37da18e99dabd9023c56b4" title="Converts the elements of the Q31 vector to floating-point vector.">arm_q31_to_float()</a></li>
</ul>
</dd></dl>
<p><b> Refer </b> <a class="el" href="arm_graphic_equalizer_example_q31_8c-example.html">arm_graphic_equalizer_example_q31.c</a> </p>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:50 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
