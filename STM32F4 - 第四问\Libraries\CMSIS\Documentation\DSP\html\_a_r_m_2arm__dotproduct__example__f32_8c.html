<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>arm_dotproduct_example_f32.c File Reference</title>
<title>CMSIS-DSP: arm_dotproduct_example_f32.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('_a_r_m_2arm__dotproduct__example__f32_8c.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">ARM/arm_dotproduct_example_f32.c File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:af8a1d2ed31f7c9a00fec46a798edb61b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a></td></tr>
<tr class="separator:af8a1d2ed31f7c9a00fec46a798edb61b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fd2b1bcd7ddcf506237987ad780f495"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a3fd2b1bcd7ddcf506237987ad780f495">DELTA</a></td></tr>
<tr class="separator:a3fd2b1bcd7ddcf506237987ad780f495"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a52d2cba30e6946c95578be946ac12a65"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main</a> (void)</td></tr>
<tr class="separator:a52d2cba30e6946c95578be946ac12a65"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a0c248a472fdc0507e4ab7d693e4876b6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a0c248a472fdc0507e4ab7d693e4876b6">srcA_buf_f32</a> [<a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>]</td></tr>
<tr class="separator:a0c248a472fdc0507e4ab7d693e4876b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67d9082c1585d4854ae9ca38db170ff5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a67d9082c1585d4854ae9ca38db170ff5">srcB_buf_f32</a> [<a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>]</td></tr>
<tr class="separator:a67d9082c1585d4854ae9ca38db170ff5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad57c1f9ad68d098d79b15ec6844a26fc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#ad57c1f9ad68d098d79b15ec6844a26fc">refDotProdOut</a></td></tr>
<tr class="separator:ad57c1f9ad68d098d79b15ec6844a26fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0bfd425dfe1ff2bda80fb957e464098"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#ad0bfd425dfe1ff2bda80fb957e464098">multOutput</a> [<a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>]</td></tr>
<tr class="separator:ad0bfd425dfe1ff2bda80fb957e464098"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a324833b61eae796082e07d078a67c34f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a324833b61eae796082e07d078a67c34f">testOutput</a></td></tr>
<tr class="separator:a324833b61eae796082e07d078a67c34f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88ccb294236ab22b00310c47164c53c3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a></td></tr>
<tr class="separator:a88ccb294236ab22b00310c47164c53c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="a3fd2b1bcd7ddcf506237987ad780f495"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define DELTA</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="af8a1d2ed31f7c9a00fec46a798edb61b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define MAX_BLOCKSIZE</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Referenced by <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="a52d2cba30e6946c95578be946ac12a65"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t main </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>References <a class="el" href="group___basic_add.html#ga6a904a547413b10565dd1d251c6bafbd">arm_add_f32()</a>, <a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6a09457f2be656b35015fd6d36202fa376">ARM_MATH_TEST_FAILURE</a>, <a class="el" href="group___basic_mult.html#gaca3f0b8227da431ab29225b88888aa32">arm_mult_f32()</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a3fd2b1bcd7ddcf506237987ad780f495">DELTA</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#ad0bfd425dfe1ff2bda80fb957e464098">multOutput</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#ad57c1f9ad68d098d79b15ec6844a26fc">refDotProdOut</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a0c248a472fdc0507e4ab7d693e4876b6">srcA_buf_f32</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a67d9082c1585d4854ae9ca38db170ff5">srcB_buf_f32</a>, <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a88ccb294236ab22b00310c47164c53c3">status</a>, and <a class="el" href="_a_r_m_2arm__class__marks__example__f32_8c.html#afd4d61aad5f35a4e42d580004e2f9a1d">testOutput</a>.</p>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a class="anchor" id="ad0bfd425dfe1ff2bda80fb957e464098"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> multOutput[<a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_dotproduct_example_f32_8c-example.html#a4">arm_dotproduct_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="ad57c1f9ad68d098d79b15ec6844a26fc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> refDotProdOut</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_dotproduct_example_f32_8c-example.html#a3">arm_dotproduct_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a0c248a472fdc0507e4ab7d693e4876b6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> srcA_buf_f32[<a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_dotproduct_example_f32_8c-example.html#a0">arm_dotproduct_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a67d9082c1585d4854ae9ca38db170ff5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> srcB_buf_f32[<a class="el" href="arm__variance__example__f32_8c.html#af8a1d2ed31f7c9a00fec46a798edb61b">MAX_BLOCKSIZE</a>]</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_dotproduct_example_f32_8c-example.html#a2">arm_dotproduct_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a88ccb294236ab22b00310c47164c53c3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a5e459c6409dfcd2927bb8a57491d7cf6">arm_status</a> status</td>
        </tr>
      </table>
</div><div class="memdoc">
<dl><dt><b>Examples: </b></dt><dd><a class="el" href="arm_convolution_example_f32_8c-example.html#a12">arm_convolution_example_f32.c</a>, <a class="el" href="arm_dotproduct_example_f32_8c-example.html#a6">arm_dotproduct_example_f32.c</a>, <a class="el" href="arm_fft_bin_example_f32_8c-example.html#a9">arm_fft_bin_example_f32.c</a>, <a class="el" href="arm_fir_example_f32_8c-example.html#a13">arm_fir_example_f32.c</a>, <a class="el" href="arm_graphic_equalizer_example_q31_8c-example.html#a18">arm_graphic_equalizer_example_q31.c</a>, <a class="el" href="arm_linear_interp_example_f32_8c-example.html#a9">arm_linear_interp_example_f32.c</a>, <a class="el" href="arm_matrix_example_f32_8c-example.html#a10">arm_matrix_example_f32.c</a>, <a class="el" href="arm_signal_converge_example_f32_8c-example.html#a22">arm_signal_converge_example_f32.c</a>, <a class="el" href="arm_sin_cos_example_f32_8c-example.html#a9">arm_sin_cos_example_f32.c</a>, and <a class="el" href="arm_variance_example_f32_8c-example.html#a8">arm_variance_example_f32.c</a>.</dd>
</dl>
<p>Referenced by <a class="el" href="group___complex_f_f_t.html#gac9565e6bc7229577ecf5e090313cafd7">arm_cfft_radix2_init_f32()</a>, <a class="el" href="group___complex_f_f_t.html#ga5c5b2127b3c4ea2d03692127f8543858">arm_cfft_radix2_init_q15()</a>, <a class="el" href="group___complex_f_f_t.html#gabec9611e77382f31e152668bf6b4b638">arm_cfft_radix2_init_q31()</a>, <a class="el" href="group___complex_f_f_t.html#gaf336459f684f0b17bfae539ef1b1b78a">arm_cfft_radix4_init_f32()</a>, <a class="el" href="group___complex_f_f_t.html#ga0c2acfda3126c452e75b81669e8ad9ef">arm_cfft_radix4_init_q15()</a>, <a class="el" href="group___complex_f_f_t.html#gad5caaafeec900c8ff72321c01bbd462c">arm_cfft_radix4_init_q31()</a>, <a class="el" href="group___partial_conv.html#ga16d10f32072cd79fc5fb6e785df45f5e">arm_conv_partial_f32()</a>, <a class="el" href="group___partial_conv.html#ga3de9c4ddcc7886de25b70d875099a8d9">arm_conv_partial_fast_opt_q15()</a>, <a class="el" href="group___partial_conv.html#ga1e4d43385cb62262a78c6752fe1fafb2">arm_conv_partial_fast_q15()</a>, <a class="el" href="group___partial_conv.html#ga10c5294cda8c4985386f4e3944be7650">arm_conv_partial_fast_q31()</a>, <a class="el" href="group___partial_conv.html#ga834b23b4ade8682beeb55778399101f8">arm_conv_partial_opt_q15()</a>, <a class="el" href="group___partial_conv.html#ga3707e16af1435b215840006a7ab0c98f">arm_conv_partial_opt_q7()</a>, <a class="el" href="group___partial_conv.html#ga209a2a913a0c5e5679c5988da8f46b03">arm_conv_partial_q15()</a>, <a class="el" href="group___partial_conv.html#ga78e73a5f02d103168a09821fb461e77a">arm_conv_partial_q31()</a>, <a class="el" href="group___partial_conv.html#ga8567259fe18396dd972242c41741ebf4">arm_conv_partial_q7()</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#gab094ad3bc6fa1b84e8b12a24e1850a06">arm_dct4_init_f32()</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga966fd1b66a80873964533703ab5dc054">arm_dct4_init_q15()</a>, <a class="el" href="group___d_c_t4___i_d_c_t4.html#ga631bb59c7c97c814ff7147ecba6a716a">arm_dct4_init_q31()</a>, <a class="el" href="group___f_i_r__decimate.html#gaaa2524b08220fd6c3f753e692ffc7d3b">arm_fir_decimate_init_f32()</a>, <a class="el" href="group___f_i_r__decimate.html#gada660e54b93d5d32178c6f5e1c6f368d">arm_fir_decimate_init_q15()</a>, <a class="el" href="group___f_i_r__decimate.html#ga9ed47c4e0f58affa935d84e0508a7f39">arm_fir_decimate_init_q31()</a>, <a class="el" href="group___f_i_r.html#gae2a50f692f41ba57e44ed0719b1368bd">arm_fir_init_q15()</a>, <a class="el" href="group___f_i_r___interpolate.html#ga0f857457a815946f7e4dca989ebf6ff6">arm_fir_interpolate_init_f32()</a>, <a class="el" href="group___f_i_r___interpolate.html#ga18e8c4a74ff1d0f88876cc63f675288f">arm_fir_interpolate_init_q15()</a>, <a class="el" href="group___f_i_r___interpolate.html#ga9d0ba38ce9f12a850dd242731d307476">arm_fir_interpolate_init_q31()</a>, <a class="el" href="group___matrix_add.html#ga04bbf64a5f9c9e57dd1efb26a768aba1">arm_mat_add_f32()</a>, <a class="el" href="group___matrix_add.html#ga147e90b7c12a162735ab8824127a33ee">arm_mat_add_q15()</a>, <a class="el" href="group___matrix_add.html#ga7d9d7d81a0832a17b831aad1e4a5dc16">arm_mat_add_q31()</a>, <a class="el" href="group___cmplx_matrix_mult.html#ga1adb839ac84445b8c2f04efa43faef35">arm_mat_cmplx_mult_f32()</a>, <a class="el" href="group___cmplx_matrix_mult.html#ga63066615e7d6f6a44f4358725092419e">arm_mat_cmplx_mult_q15()</a>, <a class="el" href="group___cmplx_matrix_mult.html#gaaf3c0b171ca8412c77bab9fa90804737">arm_mat_cmplx_mult_q31()</a>, <a class="el" href="group___matrix_inv.html#ga542be7aabbf7a2297a4b62cf212910e3">arm_mat_inverse_f32()</a>, <a class="el" href="group___matrix_inv.html#gaede2367c02df083cc915ddd5d8fae838">arm_mat_inverse_f64()</a>, <a class="el" href="group___matrix_mult.html#ga917bf0270310c1d3f0eda1fc7c0026a0">arm_mat_mult_f32()</a>, <a class="el" href="group___matrix_mult.html#ga08f37d93a5bfef0c5000dc5e0a411f93">arm_mat_mult_fast_q15()</a>, <a class="el" href="group___matrix_mult.html#ga2785e8c1b785348b0c439b56aaf585a3">arm_mat_mult_fast_q31()</a>, <a class="el" href="group___matrix_mult.html#ga3657b99a9667945373e520dbac0f4516">arm_mat_mult_q15()</a>, <a class="el" href="group___matrix_mult.html#ga2ec612a8c2c4916477fb9bc1ab548a6e">arm_mat_mult_q31()</a>, <a class="el" href="group___matrix_scale.html#ga9cb4e385b18c9a0b9cbc940c1067ca12">arm_mat_scale_f32()</a>, <a class="el" href="group___matrix_scale.html#ga7521769e2cf1c3d9c4656138cd2ae2ca">arm_mat_scale_q15()</a>, <a class="el" href="group___matrix_scale.html#ga609743821ee81fa8c34c4bcdc1ed9744">arm_mat_scale_q31()</a>, <a class="el" href="group___matrix_sub.html#gac8b72fb70246ccfee3b372002345732c">arm_mat_sub_f32()</a>, <a class="el" href="group___matrix_sub.html#gaf647776a425b7f9dd0aca3e11d81f02f">arm_mat_sub_q15()</a>, <a class="el" href="group___matrix_sub.html#ga39f42e0e3b7f115fbb909d6ff4e1329d">arm_mat_sub_q31()</a>, <a class="el" href="group___matrix_trans.html#gad7dd9f108429da13d3864696ceeec789">arm_mat_trans_f32()</a>, <a class="el" href="group___matrix_trans.html#ga4f4f821cc695fd0ef9061d702e08050a">arm_mat_trans_q15()</a>, <a class="el" href="group___matrix_trans.html#ga30a4d49489ac67ff98a46b9f58f73bf1">arm_mat_trans_q31()</a>, <a class="el" href="group___real_f_f_t.html#gac5fceb172551e7c11eb4d0e17ef15aa3">arm_rfft_fast_init_f32()</a>, <a class="el" href="group___real_f_f_t.html#ga10717ee326bf50832ef1c25b85a23068">arm_rfft_init_f32()</a>, <a class="el" href="group___real_f_f_t.html#ga053450cc600a55410ba5b5605e96245d">arm_rfft_init_q15()</a>, <a class="el" href="group___real_f_f_t.html#ga5abde938abbe72e95c5bab080eb33c45">arm_rfft_init_q31()</a>, and <a class="el" href="_a_r_m_2arm__convolution__example__f32_8c.html#a52d2cba30e6946c95578be946ac12a65">main()</a>.</p>

</div>
</div>
<a class="anchor" id="a324833b61eae796082e07d078a67c34f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="arm__math_8h.html#a4611b605e45ab401f02cab15c5e38715">float32_t</a> testOutput</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_be2d3df67661aefe0e3f0071a1d6f8f1.html">DSP_Lib</a></li><li class="navelem"><a class="el" href="dir_50f4d4f91ce5cd72cb6928b47e85a7f8.html">Examples</a></li><li class="navelem"><a class="el" href="dir_d1d61a1361fc579da85c1b709ed868d7.html">arm_dotproduct_example</a></li><li class="navelem"><a class="el" href="dir_eaa4f497a3f9bf201d37aaf8d8603deb.html">ARM</a></li><li class="navelem"><a class="el" href="_a_r_m_2arm__dotproduct__example__f32_8c.html">arm_dotproduct_example_f32.c</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:49 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
