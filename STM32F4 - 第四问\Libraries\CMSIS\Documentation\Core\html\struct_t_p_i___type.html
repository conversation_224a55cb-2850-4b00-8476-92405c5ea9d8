<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>TPI_Type Struct Reference</title>
<title>CMSIS-CORE: TPI_Type Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-CORE
   &#160;<span id="projectnumber">Version 4.10</span>
   </div>
   <div id="projectbrief">CMSIS-CORE support for Cortex-M processor-based devices</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('struct_t_p_i___type.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">TPI_Type Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Structure type to access the Trace Port Interface Register (TPI).  
</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a158e9d784f6ee6398f4bdcb2e4ca0912"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a158e9d784f6ee6398f4bdcb2e4ca0912">SSPSR</a></td></tr>
<tr class="memdesc:a158e9d784f6ee6398f4bdcb2e4ca0912"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x000 (R/ ) Supported Parallel Port Size Register.  <a href="#a158e9d784f6ee6398f4bdcb2e4ca0912"></a><br/></td></tr>
<tr class="separator:a158e9d784f6ee6398f4bdcb2e4ca0912"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa723ef3d38237aa2465779b3cc73a94a"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#aa723ef3d38237aa2465779b3cc73a94a">CSPSR</a></td></tr>
<tr class="memdesc:aa723ef3d38237aa2465779b3cc73a94a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x004 (R/W) Current Parallel Port Size Register.  <a href="#aa723ef3d38237aa2465779b3cc73a94a"></a><br/></td></tr>
<tr class="separator:aa723ef3d38237aa2465779b3cc73a94a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af143c5e8fc9a3b2be2878e9c1f331aa9"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#af143c5e8fc9a3b2be2878e9c1f331aa9">RESERVED0</a> [2]</td></tr>
<tr class="memdesc:af143c5e8fc9a3b2be2878e9c1f331aa9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#af143c5e8fc9a3b2be2878e9c1f331aa9"></a><br/></td></tr>
<tr class="separator:af143c5e8fc9a3b2be2878e9c1f331aa9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad75832a669eb121f6fce3c28d36b7fab"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#ad75832a669eb121f6fce3c28d36b7fab">ACPR</a></td></tr>
<tr class="memdesc:ad75832a669eb121f6fce3c28d36b7fab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x010 (R/W) Asynchronous Clock Prescaler Register.  <a href="#ad75832a669eb121f6fce3c28d36b7fab"></a><br/></td></tr>
<tr class="separator:ad75832a669eb121f6fce3c28d36b7fab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3956fe93987b725d89d3be32738da12"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#ac3956fe93987b725d89d3be32738da12">RESERVED1</a> [55]</td></tr>
<tr class="memdesc:ac3956fe93987b725d89d3be32738da12"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#ac3956fe93987b725d89d3be32738da12"></a><br/></td></tr>
<tr class="separator:ac3956fe93987b725d89d3be32738da12"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3eb655f2e45d7af358775025c1a50c8e"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a3eb655f2e45d7af358775025c1a50c8e">SPPR</a></td></tr>
<tr class="memdesc:a3eb655f2e45d7af358775025c1a50c8e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x0F0 (R/W) Selected Pin Protocol Register.  <a href="#a3eb655f2e45d7af358775025c1a50c8e"></a><br/></td></tr>
<tr class="separator:a3eb655f2e45d7af358775025c1a50c8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7bbb92e6231b9b38ac483f7d161a096"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#ac7bbb92e6231b9b38ac483f7d161a096">RESERVED2</a> [131]</td></tr>
<tr class="memdesc:ac7bbb92e6231b9b38ac483f7d161a096"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#ac7bbb92e6231b9b38ac483f7d161a096"></a><br/></td></tr>
<tr class="separator:ac7bbb92e6231b9b38ac483f7d161a096"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae67849b2c1016fe6ef9095827d16cddd"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#ae67849b2c1016fe6ef9095827d16cddd">FFSR</a></td></tr>
<tr class="memdesc:ae67849b2c1016fe6ef9095827d16cddd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x300 (R/ ) Formatter and Flush Status Register.  <a href="#ae67849b2c1016fe6ef9095827d16cddd"></a><br/></td></tr>
<tr class="separator:ae67849b2c1016fe6ef9095827d16cddd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3eb42d69922e340037692424a69da880"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a3eb42d69922e340037692424a69da880">FFCR</a></td></tr>
<tr class="memdesc:a3eb42d69922e340037692424a69da880"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x304 (R/W) Formatter and Flush Control Register.  <a href="#a3eb42d69922e340037692424a69da880"></a><br/></td></tr>
<tr class="separator:a3eb42d69922e340037692424a69da880"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a377b78fe804f327e6f8b3d0f37e7bfef"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a377b78fe804f327e6f8b3d0f37e7bfef">FSCR</a></td></tr>
<tr class="memdesc:a377b78fe804f327e6f8b3d0f37e7bfef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0x308 (R/ ) Formatter Synchronization Counter Register.  <a href="#a377b78fe804f327e6f8b3d0f37e7bfef"></a><br/></td></tr>
<tr class="separator:a377b78fe804f327e6f8b3d0f37e7bfef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31700c8cdd26e4c094db72af33d9f24c"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a31700c8cdd26e4c094db72af33d9f24c">RESERVED3</a> [759]</td></tr>
<tr class="memdesc:a31700c8cdd26e4c094db72af33d9f24c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a31700c8cdd26e4c094db72af33d9f24c"></a><br/></td></tr>
<tr class="separator:a31700c8cdd26e4c094db72af33d9f24c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa4b603c71768dbda553da571eccba1fe"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#aa4b603c71768dbda553da571eccba1fe">TRIGGER</a></td></tr>
<tr class="memdesc:aa4b603c71768dbda553da571eccba1fe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xEE8 (R/ ) TRIGGER.  <a href="#aa4b603c71768dbda553da571eccba1fe"></a><br/></td></tr>
<tr class="separator:aa4b603c71768dbda553da571eccba1fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae91ff529e87d8e234343ed31bcdc4f10"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#ae91ff529e87d8e234343ed31bcdc4f10">FIFO0</a></td></tr>
<tr class="memdesc:ae91ff529e87d8e234343ed31bcdc4f10"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xEEC (R/ ) Integration ETM Data.  <a href="#ae91ff529e87d8e234343ed31bcdc4f10"></a><br/></td></tr>
<tr class="separator:ae91ff529e87d8e234343ed31bcdc4f10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a176d991adb4c022bd5b982a9f8fa6a1d"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a176d991adb4c022bd5b982a9f8fa6a1d">ITATBCTR2</a></td></tr>
<tr class="memdesc:a176d991adb4c022bd5b982a9f8fa6a1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xEF0 (R/ ) ITATBCTR2.  <a href="#a176d991adb4c022bd5b982a9f8fa6a1d"></a><br/></td></tr>
<tr class="separator:a176d991adb4c022bd5b982a9f8fa6a1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a684071216fafee4e80be6aaa932cec46"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a684071216fafee4e80be6aaa932cec46">RESERVED4</a> [1]</td></tr>
<tr class="memdesc:a684071216fafee4e80be6aaa932cec46"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a684071216fafee4e80be6aaa932cec46"></a><br/></td></tr>
<tr class="separator:a684071216fafee4e80be6aaa932cec46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a20ca7fad4d4009c242f20a7b4a44b7d0"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a20ca7fad4d4009c242f20a7b4a44b7d0">ITATBCTR0</a></td></tr>
<tr class="memdesc:a20ca7fad4d4009c242f20a7b4a44b7d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xEF8 (R/ ) ITATBCTR0.  <a href="#a20ca7fad4d4009c242f20a7b4a44b7d0"></a><br/></td></tr>
<tr class="separator:a20ca7fad4d4009c242f20a7b4a44b7d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aebaa9b8dd27f8017dd4f92ecf32bac8e"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#aebaa9b8dd27f8017dd4f92ecf32bac8e">FIFO1</a></td></tr>
<tr class="memdesc:aebaa9b8dd27f8017dd4f92ecf32bac8e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xEFC (R/ ) Integration ITM Data.  <a href="#aebaa9b8dd27f8017dd4f92ecf32bac8e"></a><br/></td></tr>
<tr class="separator:aebaa9b8dd27f8017dd4f92ecf32bac8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab49c2cb6b5fe082746a444e07548c198"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#ab49c2cb6b5fe082746a444e07548c198">ITCTRL</a></td></tr>
<tr class="memdesc:ab49c2cb6b5fe082746a444e07548c198"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xF00 (R/W) Integration Mode Control.  <a href="#ab49c2cb6b5fe082746a444e07548c198"></a><br/></td></tr>
<tr class="separator:ab49c2cb6b5fe082746a444e07548c198"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f80dd93f6bab6524603a7aa58de9a30"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a3f80dd93f6bab6524603a7aa58de9a30">RESERVED5</a> [39]</td></tr>
<tr class="memdesc:a3f80dd93f6bab6524603a7aa58de9a30"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a3f80dd93f6bab6524603a7aa58de9a30"></a><br/></td></tr>
<tr class="separator:a3f80dd93f6bab6524603a7aa58de9a30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e4d5a07fabd771fa942a171230a0a84"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a2e4d5a07fabd771fa942a171230a0a84">CLAIMSET</a></td></tr>
<tr class="memdesc:a2e4d5a07fabd771fa942a171230a0a84"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xFA0 (R/W) Claim tag set.  <a href="#a2e4d5a07fabd771fa942a171230a0a84"></a><br/></td></tr>
<tr class="separator:a2e4d5a07fabd771fa942a171230a0a84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44efa6045512c8d4da64b0623f7a43ad"><td class="memItemLeft" align="right" valign="top">__IO uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a44efa6045512c8d4da64b0623f7a43ad">CLAIMCLR</a></td></tr>
<tr class="memdesc:a44efa6045512c8d4da64b0623f7a43ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xFA4 (R/W) Claim tag clear.  <a href="#a44efa6045512c8d4da64b0623f7a43ad"></a><br/></td></tr>
<tr class="separator:a44efa6045512c8d4da64b0623f7a43ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a476ca23fbc9480f1697fbec871130550"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a476ca23fbc9480f1697fbec871130550">RESERVED7</a> [8]</td></tr>
<tr class="memdesc:a476ca23fbc9480f1697fbec871130550"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reserved.  <a href="#a476ca23fbc9480f1697fbec871130550"></a><br/></td></tr>
<tr class="separator:a476ca23fbc9480f1697fbec871130550"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4b2e0d680cf7e26728ca8966363a938d"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a4b2e0d680cf7e26728ca8966363a938d">DEVID</a></td></tr>
<tr class="memdesc:a4b2e0d680cf7e26728ca8966363a938d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xFC8 (R/ ) TPIU_DEVID.  <a href="#a4b2e0d680cf7e26728ca8966363a938d"></a><br/></td></tr>
<tr class="separator:a4b2e0d680cf7e26728ca8966363a938d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16d12c5b1e12f764fa3ec4a51c5f0f35"><td class="memItemLeft" align="right" valign="top">__I uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_t_p_i___type.html#a16d12c5b1e12f764fa3ec4a51c5f0f35">DEVTYPE</a></td></tr>
<tr class="memdesc:a16d12c5b1e12f764fa3ec4a51c5f0f35"><td class="mdescLeft">&#160;</td><td class="mdescRight">Offset: 0xFCC (R/ ) TPIU_DEVTYPE.  <a href="#a16d12c5b1e12f764fa3ec4a51c5f0f35"></a><br/></td></tr>
<tr class="separator:a16d12c5b1e12f764fa3ec4a51c5f0f35"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Field Documentation</h2>
<a class="anchor" id="ad75832a669eb121f6fce3c28d36b7fab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t TPI_Type::ACPR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a44efa6045512c8d4da64b0623f7a43ad"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t TPI_Type::CLAIMCLR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a2e4d5a07fabd771fa942a171230a0a84"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t TPI_Type::CLAIMSET</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa723ef3d38237aa2465779b3cc73a94a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t TPI_Type::CSPSR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4b2e0d680cf7e26728ca8966363a938d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t TPI_Type::DEVID</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a16d12c5b1e12f764fa3ec4a51c5f0f35"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t TPI_Type::DEVTYPE</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3eb42d69922e340037692424a69da880"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t TPI_Type::FFCR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae67849b2c1016fe6ef9095827d16cddd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t TPI_Type::FFSR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae91ff529e87d8e234343ed31bcdc4f10"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t TPI_Type::FIFO0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aebaa9b8dd27f8017dd4f92ecf32bac8e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t TPI_Type::FIFO1</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a377b78fe804f327e6f8b3d0f37e7bfef"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t TPI_Type::FSCR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a20ca7fad4d4009c242f20a7b4a44b7d0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t TPI_Type::ITATBCTR0</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a176d991adb4c022bd5b982a9f8fa6a1d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t TPI_Type::ITATBCTR2</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab49c2cb6b5fe082746a444e07548c198"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t TPI_Type::ITCTRL</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af143c5e8fc9a3b2be2878e9c1f331aa9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t TPI_Type::RESERVED0[2]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac3956fe93987b725d89d3be32738da12"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t TPI_Type::RESERVED1[55]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac7bbb92e6231b9b38ac483f7d161a096"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t TPI_Type::RESERVED2[131]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a31700c8cdd26e4c094db72af33d9f24c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t TPI_Type::RESERVED3[759]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a684071216fafee4e80be6aaa932cec46"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t TPI_Type::RESERVED4[1]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3f80dd93f6bab6524603a7aa58de9a30"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t TPI_Type::RESERVED5[39]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a476ca23fbc9480f1697fbec871130550"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t TPI_Type::RESERVED7[8]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3eb655f2e45d7af358775025c1a50c8e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t TPI_Type::SPPR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a158e9d784f6ee6398f4bdcb2e4ca0912"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__IO uint32_t TPI_Type::SSPSR</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa4b603c71768dbda553da571eccba1fe"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__I uint32_t TPI_Type::TRIGGER</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_t_p_i___type.html">TPI_Type</a></li>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:40 for CMSIS-CORE by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
