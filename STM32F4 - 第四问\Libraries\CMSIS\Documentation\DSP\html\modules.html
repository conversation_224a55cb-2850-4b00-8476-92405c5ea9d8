<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<title>Reference</title>
<title>CMSIS-DSP: Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<link href="cmsis.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="printComponentTabs.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { searchBox.OnSelectItem(0); });
</script>
<link href="stylsheetf" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 46px;">
  <td id="projectlogo"><img alt="Logo" src="CMSIS_Logo_Final.png"/></td>
  <td style="padding-left: 0.5em;">
   <div id="projectname">CMSIS-DSP
   &#160;<span id="projectnumber">Version 1.4.5</span>
   </div>
   <div id="projectbrief">CMSIS DSP Software Library</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
    <ul class="tablist">
      <script type="text/javascript">
		<!--
		writeComponentTabs.call(this);
		//-->
      </script>
	  </ul>
</div>
<!-- Generated by Doxygen 1.8.2 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Usage&#160;and&#160;Description</span></a></li>
      <li class="current"><a href="modules.html"><span>Reference</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('modules.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
<a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(0)"><span class="SelectionMark">&#160;</span>All</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(1)"><span class="SelectionMark">&#160;</span>Data Structures</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(2)"><span class="SelectionMark">&#160;</span>Files</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(3)"><span class="SelectionMark">&#160;</span>Functions</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(4)"><span class="SelectionMark">&#160;</span>Variables</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(5)"><span class="SelectionMark">&#160;</span>Typedefs</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(6)"><span class="SelectionMark">&#160;</span>Enumerations</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(7)"><span class="SelectionMark">&#160;</span>Enumerator</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(8)"><span class="SelectionMark">&#160;</span>Macros</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(9)"><span class="SelectionMark">&#160;</span>Groups</a><a class="SelectItem" href="javascript:void(0)" onclick="searchBox.OnSelectItem(10)"><span class="SelectionMark">&#160;</span>Pages</a></div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">Here is a list of all modules:</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><img id="arr_0_" src="ftv2mnode.png" alt="o" width="16" height="22" onclick="toggleFolder('0_')"/><a class="el" href="group__group_math.html" target="_self">Basic Math Functions</a></td><td class="desc"></td></tr>
<tr id="row_0_0_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___basic_abs.html" target="_self">Vector Absolute Value</a></td><td class="desc"></td></tr>
<tr id="row_0_1_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___basic_add.html" target="_self">Vector Addition</a></td><td class="desc"></td></tr>
<tr id="row_0_2_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__dot__prod.html" target="_self">Vector Dot Product</a></td><td class="desc"></td></tr>
<tr id="row_0_3_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___basic_mult.html" target="_self">Vector Multiplication</a></td><td class="desc"></td></tr>
<tr id="row_0_4_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__negate.html" target="_self">Vector Negate</a></td><td class="desc"></td></tr>
<tr id="row_0_5_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__offset.html" target="_self">Vector Offset</a></td><td class="desc"></td></tr>
<tr id="row_0_6_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__scale.html" target="_self">Vector Scale</a></td><td class="desc"></td></tr>
<tr id="row_0_7_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__shift.html" target="_self">Vector Shift</a></td><td class="desc"></td></tr>
<tr id="row_0_8_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><a class="el" href="group___basic_sub.html" target="_self">Vector Subtraction</a></td><td class="desc"></td></tr>
<tr id="row_1_" class="even"><td class="entry"><img id="arr_1_" src="ftv2mnode.png" alt="o" width="16" height="22" onclick="toggleFolder('1_')"/><a class="el" href="group__group_fast_math.html" target="_self">Fast Math Functions</a></td><td class="desc"></td></tr>
<tr id="row_1_0_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__cos.html" target="_self">Cosine</a></td><td class="desc"></td></tr>
<tr id="row_1_1_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__sin.html" target="_self">Sine</a></td><td class="desc"></td></tr>
<tr id="row_1_2_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><a class="el" href="group___s_q_r_t.html" target="_self">Square Root</a></td><td class="desc"></td></tr>
<tr id="row_2_" class="even"><td class="entry"><img id="arr_2_" src="ftv2mnode.png" alt="o" width="16" height="22" onclick="toggleFolder('2_')"/><a class="el" href="group__group_cmplx_math.html" target="_self">Complex Math Functions</a></td><td class="desc"></td></tr>
<tr id="row_2_0_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__cmplx__conj.html" target="_self">Complex Conjugate</a></td><td class="desc"></td></tr>
<tr id="row_2_1_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__cmplx__dot__prod.html" target="_self">Complex Dot Product</a></td><td class="desc"></td></tr>
<tr id="row_2_2_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__cmplx__mag.html" target="_self">Complex Magnitude</a></td><td class="desc"></td></tr>
<tr id="row_2_3_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__cmplx__mag__squared.html" target="_self">Complex Magnitude Squared</a></td><td class="desc"></td></tr>
<tr id="row_2_4_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___cmplx_by_cmplx_mult.html" target="_self">Complex-by-Complex Multiplication</a></td><td class="desc"></td></tr>
<tr id="row_2_5_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><a class="el" href="group___cmplx_by_real_mult.html" target="_self">Complex-by-Real Multiplication</a></td><td class="desc"></td></tr>
<tr id="row_3_"><td class="entry"><img id="arr_3_" src="ftv2mnode.png" alt="o" width="16" height="22" onclick="toggleFolder('3_')"/><a class="el" href="group__group_filters.html" target="_self">Filtering Functions</a></td><td class="desc"></td></tr>
<tr id="row_3_0_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___biquad_cascade_d_f1__32x64.html" target="_self">High Precision Q31 Biquad Cascade Filter</a></td><td class="desc"></td></tr>
<tr id="row_3_1_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___biquad_cascade_d_f1.html" target="_self">Biquad Cascade IIR Filters Using Direct Form I Structure</a></td><td class="desc"></td></tr>
<tr id="row_3_2_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___biquad_cascade_d_f2_t.html" target="_self">Biquad Cascade IIR Filters Using a Direct Form II Transposed Structure</a></td><td class="desc"></td></tr>
<tr id="row_3_3_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___conv.html" target="_self">Convolution</a></td><td class="desc"></td></tr>
<tr id="row_3_4_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___partial_conv.html" target="_self">Partial Convolution</a></td><td class="desc"></td></tr>
<tr id="row_3_5_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___corr.html" target="_self">Correlation</a></td><td class="desc"></td></tr>
<tr id="row_3_6_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___f_i_r__decimate.html" target="_self">Finite Impulse Response (FIR) Decimator</a></td><td class="desc"></td></tr>
<tr id="row_3_7_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___f_i_r.html" target="_self">Finite Impulse Response (FIR) Filters</a></td><td class="desc"></td></tr>
<tr id="row_3_8_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___f_i_r___lattice.html" target="_self">Finite Impulse Response (FIR) Lattice Filters</a></td><td class="desc"></td></tr>
<tr id="row_3_9_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___f_i_r___sparse.html" target="_self">Finite Impulse Response (FIR) Sparse Filters</a></td><td class="desc"></td></tr>
<tr id="row_3_10_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___i_i_r___lattice.html" target="_self">Infinite Impulse Response (IIR) Lattice Filters</a></td><td class="desc"></td></tr>
<tr id="row_3_11_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___l_m_s.html" target="_self">Least Mean Square (LMS) Filters</a></td><td class="desc"></td></tr>
<tr id="row_3_12_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___l_m_s___n_o_r_m.html" target="_self">Normalized LMS Filters</a></td><td class="desc"></td></tr>
<tr id="row_3_13_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><a class="el" href="group___f_i_r___interpolate.html" target="_self">Finite Impulse Response (FIR) Interpolator</a></td><td class="desc"></td></tr>
<tr id="row_4_" class="even"><td class="entry"><img id="arr_4_" src="ftv2mnode.png" alt="o" width="16" height="22" onclick="toggleFolder('4_')"/><a class="el" href="group__group_matrix.html" target="_self">Matrix Functions</a></td><td class="desc"></td></tr>
<tr id="row_4_0_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___matrix_add.html" target="_self">Matrix Addition</a></td><td class="desc"></td></tr>
<tr id="row_4_1_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___cmplx_matrix_mult.html" target="_self">Complex Matrix Multiplication</a></td><td class="desc"></td></tr>
<tr id="row_4_2_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___matrix_init.html" target="_self">Matrix Initialization</a></td><td class="desc"></td></tr>
<tr id="row_4_3_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___matrix_inv.html" target="_self">Matrix Inverse</a></td><td class="desc"></td></tr>
<tr id="row_4_4_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___matrix_mult.html" target="_self">Matrix Multiplication</a></td><td class="desc"></td></tr>
<tr id="row_4_5_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___matrix_scale.html" target="_self">Matrix Scale</a></td><td class="desc"></td></tr>
<tr id="row_4_6_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___matrix_sub.html" target="_self">Matrix Subtraction</a></td><td class="desc"></td></tr>
<tr id="row_4_7_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><a class="el" href="group___matrix_trans.html" target="_self">Matrix Transpose</a></td><td class="desc"></td></tr>
<tr id="row_5_"><td class="entry"><img id="arr_5_" src="ftv2mnode.png" alt="o" width="16" height="22" onclick="toggleFolder('5_')"/><a class="el" href="group__group_transforms.html" target="_self">Transform Functions</a></td><td class="desc"></td></tr>
<tr id="row_5_0_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___complex_f_f_t.html" target="_self">Complex FFT Functions</a></td><td class="desc"></td></tr>
<tr id="row_5_1_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___radix8___c_f_f_t___c_i_f_f_t.html" target="_self">Radix-8 Complex FFT Functions</a></td><td class="desc"></td></tr>
<tr id="row_5_2_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___d_c_t4___i_d_c_t4.html" target="_self">DCT Type IV Functions</a></td><td class="desc"></td></tr>
<tr id="row_5_3_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___fast.html" target="_self">Real FFT Functions</a></td><td class="desc"></td></tr>
<tr id="row_5_4_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___c_f_f_t___c_i_f_f_t.html" target="_self">Complex FFT Tables</a></td><td class="desc"></td></tr>
<tr id="row_5_5_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><a class="el" href="group___real_f_f_t.html" target="_self">RealFFT</a></td><td class="desc"></td></tr>
<tr id="row_6_" class="even"><td class="entry"><img id="arr_6_" src="ftv2mnode.png" alt="o" width="16" height="22" onclick="toggleFolder('6_')"/><a class="el" href="group__group_controller.html" target="_self">Controller Functions</a></td><td class="desc"></td></tr>
<tr id="row_6_0_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___sin_cos.html" target="_self">Sine Cosine</a></td><td class="desc"></td></tr>
<tr id="row_6_1_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___p_i_d.html" target="_self">PID Motor Control</a></td><td class="desc"></td></tr>
<tr id="row_6_2_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__clarke.html" target="_self">Vector Clarke Transform</a></td><td class="desc"></td></tr>
<tr id="row_6_3_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__inv__clarke.html" target="_self">Vector Inverse Clarke Transform</a></td><td class="desc"></td></tr>
<tr id="row_6_4_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__park.html" target="_self">Vector Park Transform</a></td><td class="desc"></td></tr>
<tr id="row_6_5_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><a class="el" href="group__inv__park.html" target="_self">Vector Inverse Park transform</a></td><td class="desc"></td></tr>
<tr id="row_7_"><td class="entry"><img id="arr_7_" src="ftv2mnode.png" alt="o" width="16" height="22" onclick="toggleFolder('7_')"/><a class="el" href="group__group_stats.html" target="_self">Statistics Functions</a></td><td class="desc"></td></tr>
<tr id="row_7_0_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___max.html" target="_self">Maximum</a></td><td class="desc"></td></tr>
<tr id="row_7_1_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__mean.html" target="_self">Mean</a></td><td class="desc"></td></tr>
<tr id="row_7_2_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___min.html" target="_self">Minimum</a></td><td class="desc"></td></tr>
<tr id="row_7_3_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__power.html" target="_self">Power</a></td><td class="desc"></td></tr>
<tr id="row_7_4_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___r_m_s.html" target="_self">Root mean square (RMS)</a></td><td class="desc"></td></tr>
<tr id="row_7_5_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___s_t_d.html" target="_self">Standard deviation</a></td><td class="desc"></td></tr>
<tr id="row_7_6_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><a class="el" href="group__variance.html" target="_self">Variance</a></td><td class="desc"></td></tr>
<tr id="row_8_"><td class="entry"><img id="arr_8_" src="ftv2mnode.png" alt="o" width="16" height="22" onclick="toggleFolder('8_')"/><a class="el" href="group__group_support.html" target="_self">Support Functions</a></td><td class="desc"></td></tr>
<tr id="row_8_0_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__copy.html" target="_self">Vector Copy</a></td><td class="desc"></td></tr>
<tr id="row_8_1_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___fill.html" target="_self">Vector Fill</a></td><td class="desc"></td></tr>
<tr id="row_8_2_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__float__to__x.html" target="_self">Convert 32-bit floating point value</a></td><td class="desc"></td></tr>
<tr id="row_8_3_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__q15__to__x.html" target="_self">Convert 16-bit Integer value</a></td><td class="desc"></td></tr>
<tr id="row_8_4_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group__q31__to__x.html" target="_self">Convert 32-bit Integer value</a></td><td class="desc"></td></tr>
<tr id="row_8_5_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><a class="el" href="group__q7__to__x.html" target="_self">Convert 8-bit Integer value</a></td><td class="desc"></td></tr>
<tr id="row_9_" class="even"><td class="entry"><img id="arr_9_" src="ftv2mnode.png" alt="o" width="16" height="22" onclick="toggleFolder('9_')"/><a class="el" href="group__group_interpolation.html" target="_self">Interpolation Functions</a></td><td class="desc"></td></tr>
<tr id="row_9_0_"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___linear_interpolate.html" target="_self">Linear Interpolation</a></td><td class="desc"></td></tr>
<tr id="row_9_1_" class="even"><td class="entry"><img src="ftv2vertline.png" alt="|" width="16" height="22" /><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><a class="el" href="group___bilinear_interpolate.html" target="_self">Bilinear Interpolation</a></td><td class="desc"></td></tr>
<tr id="row_10_"><td class="entry"><img id="arr_10_" src="ftv2mlastnode.png" alt="\" width="16" height="22" onclick="toggleFolder('10_')"/><a class="el" href="group__group_examples.html" target="_self">Examples</a></td><td class="desc"></td></tr>
<tr id="row_10_0_" class="even"><td class="entry"><img src="ftv2blank.png" alt="&#160;" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___class_marks.html" target="_self">Class Marks Example</a></td><td class="desc"></td></tr>
<tr id="row_10_1_"><td class="entry"><img src="ftv2blank.png" alt="&#160;" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___convolution_example.html" target="_self">Convolution Example</a></td><td class="desc"></td></tr>
<tr id="row_10_2_" class="even"><td class="entry"><img src="ftv2blank.png" alt="&#160;" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___dotproduct_example.html" target="_self">Dot Product Example</a></td><td class="desc"></td></tr>
<tr id="row_10_3_"><td class="entry"><img src="ftv2blank.png" alt="&#160;" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___frequency_bin.html" target="_self">Frequency Bin Example</a></td><td class="desc"></td></tr>
<tr id="row_10_4_" class="even"><td class="entry"><img src="ftv2blank.png" alt="&#160;" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___f_i_r_l_p_f.html" target="_self">FIR Lowpass Filter Example</a></td><td class="desc"></td></tr>
<tr id="row_10_5_"><td class="entry"><img src="ftv2blank.png" alt="&#160;" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___g_e_q5_band.html" target="_self">Graphic Audio Equalizer Example</a></td><td class="desc"></td></tr>
<tr id="row_10_6_" class="even"><td class="entry"><img src="ftv2blank.png" alt="&#160;" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___linear_interp_example.html" target="_self">Linear Interpolate Example</a></td><td class="desc"></td></tr>
<tr id="row_10_7_"><td class="entry"><img src="ftv2blank.png" alt="&#160;" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___matrix_example.html" target="_self">Matrix Example</a></td><td class="desc"></td></tr>
<tr id="row_10_8_" class="even"><td class="entry"><img src="ftv2blank.png" alt="&#160;" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___signal_convergence.html" target="_self">Signal Convergence Example</a></td><td class="desc"></td></tr>
<tr id="row_10_9_"><td class="entry"><img src="ftv2blank.png" alt="&#160;" width="16" height="22" /><img src="ftv2node.png" alt="o" width="16" height="22" /><a class="el" href="group___sin_cos_example.html" target="_self">SineCosine Example</a></td><td class="desc"></td></tr>
<tr id="row_10_10_" class="even"><td class="entry"><img src="ftv2blank.png" alt="&#160;" width="16" height="22" /><img src="ftv2lastnode.png" alt="\" width="16" height="22" /><a class="el" href="group___variance_example.html" target="_self">Variance Example</a></td><td class="desc"></td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated on Fri Mar 20 2015 14:58:51 for CMSIS-DSP by ARM Ltd. All rights reserved.
	<!--
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.2 
	-->
	</li>
  </ul>
</div>
</body>
</html>
