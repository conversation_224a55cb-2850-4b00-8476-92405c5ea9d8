/**
  ******************************************************************************
  * @file    afe_test.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   AFE板测试模块实现 - LTC2248 ADC测试
  ******************************************************************************
  */

#include "afe_test.h"
#include <stddef.h>

//==============================================================================
// 全局变量定义
//==============================================================================

uint16_t g_adc_buffer[ADC_BUFFER_SIZE];             // ADC数据缓存
volatile uint8_t g_data_ready_flag = 0;             // 数据准备就绪标志
volatile uint16_t g_buffer_index = 0;               // 缓存索引

//==============================================================================
// GPIO初始化
//==============================================================================

/**
  * @brief  初始化ADC数据线GPIO (PC0-PC11)
  * @param  None
  * @retval None
  */
void AFE_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIOC时钟
    RCC_AHB1PeriphClockCmd(ADC_DATA_GPIO_CLK, ENABLE);
    
    // 配置PC0-PC11为浮空输入
    GPIO_InitStructure.GPIO_Pin = ADC_DATA_PINS;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_Init(ADC_DATA_GPIO_PORT, &GPIO_InitStructure);
    
    // 使能GPIOA时钟
    RCC_AHB1PeriphClockCmd(ADC_CLK_GPIO_CLK, ENABLE);
    
    // 配置PA0为输入模式（ADC时钟）
    GPIO_InitStructure.GPIO_Pin = ADC_CLK_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_Init(ADC_CLK_GPIO_PORT, &GPIO_InitStructure);
}

//==============================================================================
// 外部中断初始化
//==============================================================================

/**
  * @brief  初始化PA0外部中断
  * @param  None
  * @retval None
  */
void AFE_EXTI_Init(void)
{
    EXTI_InitTypeDef EXTI_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // 使能SYSCFG时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_SYSCFG, ENABLE);
    
    // 连接EXTI线到GPIO引脚
    SYSCFG_EXTILineConfig(ADC_CLK_EXTI_PORT_SOURCE, ADC_CLK_EXTI_PIN_SOURCE);
    
    // 配置EXTI线
    EXTI_InitStructure.EXTI_Line = ADC_CLK_EXTI_LINE;
    EXTI_InitStructure.EXTI_Mode = EXTI_Mode_Interrupt;
    EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Rising;  // 上升沿触发
    EXTI_InitStructure.EXTI_LineCmd = ENABLE;
    EXTI_Init(&EXTI_InitStructure);
    
    // 配置NVIC
    NVIC_InitStructure.NVIC_IRQChannel = ADC_CLK_EXTI_IRQ;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

//==============================================================================
// AFE测试初始化
//==============================================================================

/**
  * @brief  AFE测试模块初始化
  * @param  None
  * @retval None
  */
void AFE_Test_Init(void)
{
    // 初始化GPIO
    AFE_GPIO_Init();
    
    // 初始化外部中断
    AFE_EXTI_Init();
    
    // 清空缓存
    AFE_ClearBuffer();
}

//==============================================================================
// 数据读取函数
//==============================================================================

/**
  * @brief  读取ADC数据
  * @param  None
  * @retval 12位ADC数据
  */
uint16_t AFE_ReadADCData(void)
{
    // 读取GPIOC的输入数据寄存器，获取12位ADC数据
    return (uint16_t)(GPIOC->IDR & ADC_DATA_MASK);
}

//==============================================================================
// 数据处理函数
//==============================================================================

/**
  * @brief  处理ADC数据
  * @param  stats: 统计结果结构体指针
  * @retval None
  */
void AFE_ProcessData(ADC_Stats_t *stats)
{
    if (g_data_ready_flag && stats != NULL)
    {
        // 关闭中断
        AFE_DisableInterrupt();

        // 计算统计数据
        AFE_CalculateStats(g_adc_buffer, ADC_BUFFER_SIZE, stats);

        // 清除标志位
        g_data_ready_flag = 0;

        // 重置缓存索引
        g_buffer_index = 0;

        // 重新开启中断
        AFE_EnableInterrupt();
    }
}

/**
  * @brief  计算ADC数据统计信息
  * @param  buffer: 数据缓存指针
  * @param  length: 数据长度
  * @param  stats: 统计结果结构体指针
  * @retval None
  */
void AFE_CalculateStats(uint16_t *buffer, uint16_t length, ADC_Stats_t *stats)
{
    uint16_t i;
    uint32_t sum = 0;
    uint16_t max_val = 0;
    uint16_t min_val = 0xFFFF;

    if (length == 0 || buffer == NULL || stats == NULL) return;
    
    // 初始化最大最小值
    max_val = buffer[0];
    min_val = buffer[0];
    
    // 遍历所有数据
    for (i = 0; i < length; i++)
    {
        uint16_t value = buffer[i];
        
        // 累加求和
        sum += value;
        
        // 更新最大值
        if (value > max_val)
        {
            max_val = value;
        }
        
        // 更新最小值
        if (value < min_val)
        {
            min_val = value;
        }
    }
    
    // 填充统计结果
    stats->current_value = buffer[length - 1];      // 最后一个采样值作为当前值
    stats->max_value = max_val;
    stats->min_value = min_val;
    stats->average_value = sum / length;
    stats->sample_count += length;
}

//==============================================================================
// 中断控制函数
//==============================================================================

/**
  * @brief  使能外部中断
  * @param  None
  * @retval None
  */
void AFE_EnableInterrupt(void)
{
    NVIC_EnableIRQ(ADC_CLK_EXTI_IRQ);
}

/**
  * @brief  禁用外部中断
  * @param  None
  * @retval None
  */
void AFE_DisableInterrupt(void)
{
    NVIC_DisableIRQ(ADC_CLK_EXTI_IRQ);
}

//==============================================================================
// 工具函数
//==============================================================================

/**
  * @brief  清空ADC缓存
  * @param  None
  * @retval None
  */
void AFE_ClearBuffer(void)
{
    uint16_t i;
    
    for (i = 0; i < ADC_BUFFER_SIZE; i++)
    {
        g_adc_buffer[i] = 0;
    }
    
    g_buffer_index = 0;
    g_data_ready_flag = 0;
}

//==============================================================================
// 外部中断服务函数
//==============================================================================

/* EXTI0_IRQHandler 在 User/stm32f4xx_it.c 统一实现，此处无需重复定义 */
